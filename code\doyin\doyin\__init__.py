# __init__.py 为初始入口文件,工程代码的入口文件.
# 抖音自动化脚本 - AScript框架启动文件

# 导入动作库常用函数
from ascript.android.action import click,slide,Touch,gesture
# 导入控件检索相关
from ascript.android.node import Selector
# 导入图色相关
from ascript.android.screen import capture,FindColors,FindImages,Ocr,get_color
# 导入系统相关
from ascript.android import system
# 环境设备相关
from ascript.android.system import R,Device
# 导入插件管理
from ascript.android import plug
# 导入UI组件
try:
    from ascript.android.ui import WebWindow, FloatWindow
    UI_AVAILABLE = True
except ImportError:
    print("⚠️  WebWindow不可用，UI功能将被禁用")
    UI_AVAILABLE = False

# 导入系统存储
try:
    from ascript.android.system import kv
    KV_AVAILABLE = True
except ImportError:
    print("⚠️  kv存储不可用，将使用文件存储")
    KV_AVAILABLE = False

import time
import os
import sys
import json
import threading
from datetime import datetime
import math

# 导入配置文件
try:
    # 添加父目录到路径以导入config
    parent_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
    if parent_dir not in sys.path:
        sys.path.insert(0, parent_dir)
    from config import *
    print("✅ 配置文件导入成功")
except ImportError:
    print("⚠️  配置文件导入失败，使用默认配置")

# 确保关键变量始终有定义（防止导入失败或部分导入）
if 'DOUYIN_PACKAGE_NAMES' not in globals():
    DOUYIN_PACKAGE_NAMES = ["com.ss.android.ugc.aweme.lite"]

if 'APP_LAUNCH_WAIT_TIME' not in globals():
    APP_LAUNCH_WAIT_TIME = 5

if 'OPERATION_INTERVAL' not in globals():
    OPERATION_INTERVAL = 2

if 'DEFAULT_SCREEN_WIDTH' not in globals():
    DEFAULT_SCREEN_WIDTH = 1080

if 'DEFAULT_SCREEN_HEIGHT' not in globals():
    DEFAULT_SCREEN_HEIGHT = 2340

if 'DEBUG_MODE' not in globals():
    DEBUG_MODE = True

# ==================== 存储工具函数 ====================

def save_config_to_file(config_data):
    """保存配置到文件"""
    try:
        config_file = os.path.join(os.path.dirname(__file__), "user_config.json")
        with open(config_file, 'w', encoding='utf-8') as f:
            json.dump(config_data, f, ensure_ascii=False, indent=2)
        return True
    except Exception as e:
        print(f"保存配置到文件失败: {e}")
        return False

def load_config_from_file():
    """从文件加载配置"""
    try:
        config_file = os.path.join(os.path.dirname(__file__), "user_config.json")
        if os.path.exists(config_file):
            with open(config_file, 'r', encoding='utf-8') as f:
                return json.load(f)
        return {}
    except Exception as e:
        print(f"从文件加载配置失败: {e}")
        return {}

def save_data(key, value):
    """统一的数据保存接口"""
    if KV_AVAILABLE:
        try:
            kv.put(key, json.dumps(value) if not isinstance(value, str) else value)
            return True
        except:
            pass

    # 使用文件存储作为备用
    return save_config_to_file({key: value})

def load_data(key, default_value="{}"):
    """统一的数据加载接口"""
    if KV_AVAILABLE:
        try:
            result = kv.get(key, default_value)
            if isinstance(result, str) and (result.startswith('{') or result.startswith('[')):
                return json.loads(result)
            return result
        except:
            pass

    # 使用文件存储作为备用
    config = load_config_from_file()
    result = config.get(key, default_value)

    # 如果default_value是字符串且看起来像JSON，尝试解析
    if isinstance(default_value, str) and (default_value.startswith('{') or default_value.startswith('[')):
        try:
            return json.loads(default_value) if result == default_value else result
        except:
            return result
    return result

print("Hello AS!")

# 确保DEBUG_MODE变量存在
if 'DEBUG_MODE' not in globals():
    DEBUG_MODE = True

if DEBUG_MODE:
    print("🐛 调试模式已启用")

def get_device_info():
    """获取设备信息"""
    try:
        device = Device()
        # 尝试不同的方法获取屏幕尺寸
        if hasattr(device, 'width') and hasattr(device, 'height'):
            screen_width = device.width()
            screen_height = device.height()
        elif hasattr(device, 'get_width') and hasattr(device, 'get_height'):
            screen_width = device.get_width()
            screen_height = device.get_height()
        else:
            # 尝试使用系统API
            from ascript.android import system
            screen_width = system.get_screen_width() if hasattr(system, 'get_screen_width') else 1080
            screen_height = system.get_screen_height() if hasattr(system, 'get_screen_height') else 2340

        if DEBUG_MODE:
            print(f"设备屏幕分辨率: {screen_width} x {screen_height}")
        return screen_width, screen_height
    except Exception as e:
        if DEBUG_MODE:
            print(f"获取设备信息失败: {e}")
        # 返回默认分辨率
        return 1080, 2340

def start_douyin():
    """启动抖音应用"""
    for package_name in DOUYIN_PACKAGE_NAMES:
        try:
            if DEBUG_MODE:
                print(f"正在尝试启动抖音应用: {package_name}")

            # 使用配置中的包名启动应用
            system.open(package_name)
            print("抖音应用启动成功!")

            # 等待应用完全启动（使用配置中的等待时间）
            time.sleep(APP_LAUNCH_WAIT_TIME)

            # 检查应用是否真正启动
            current_app = system.get_foreground_app()
            if "aweme" in current_app.lower():
                if DEBUG_MODE:
                    print(f"确认抖音应用已启动: {current_app}")
                return True
            else:
                if DEBUG_MODE:
                    print(f"当前前台应用: {current_app}")
                    print("抖音可能未完全启动，但继续执行...")
                return True

        except Exception as e:
            if DEBUG_MODE:
                print(f"启动抖音应用失败 ({package_name}): {e}")
            continue

    # 如果所有包名都失败，尝试使用应用名称
    try:
        if DEBUG_MODE:
            print("尝试使用应用名称启动...")
        system.open("抖音")
        time.sleep(APP_LAUNCH_WAIT_TIME)
        return True
    except Exception as e:
        if DEBUG_MODE:
            print(f"使用应用名称启动也失败: {e}")
        return False

def setup_hid_device():
    """设置HID设备连接"""
    try:
        print("正在加载ESP32插件...")
        # 加载ESP32插件
        plug.load("esp32")
        from esp32 import BleDevice, UsbDevice

        print("尝试连接蓝牙HID设备...")
        # 首先尝试蓝牙HID设备
        try:
            ble = BleDevice()
            if ble.is_conncted():
                print(f"蓝牙HID设备连接成功!")
                print(f"设备名称: {ble.get_name()}")
                print(f"MAC地址: {ble.get_mac_address()}")
                return ble, "bluetooth"
            else:
                print("蓝牙HID设备未连接，尝试重新连接...")
                if ble.re_connect():
                    print("蓝牙HID设备重连成功!")
                    return ble, "bluetooth"
                else:
                    print("蓝牙HID设备连接失败")
        except Exception as e:
            print(f"蓝牙HID设备连接异常: {e}")

        print("尝试连接USB HID设备...")
        # 尝试USB HID设备
        try:
            usb = UsbDevice()
            print("USB HID设备连接成功!")
            return usb, "usb"
        except Exception as e:
            print(f"USB HID设备连接失败: {e}")

        return None, None

    except Exception as e:
        print(f"设置HID设备失败: {e}")
        print("可能的原因:")
        print("1. ESP32插件未正确安装")
        print("2. ESP32设备未刷入正确固件")
        print("3. 蓝牙设备未配对或USB设备未授权")
        return None, None





def fallback_operations(screen_width, screen_height):
    """传统点击方式的备用操作"""
    print("使用传统点击方式进行抖音操作...")

    try:
        # 等待界面稳定
        time.sleep(3)

        # 操作1: 点击屏幕中央
        print("传统方式：点击屏幕中央...")
        center_x = screen_width // 2
        center_y = screen_height // 2
        click(center_x, center_y)
        time.sleep(2)

        # 操作2: 向上滑动
        print("传统方式：向上滑动...")
        start_x = center_x
        start_y = screen_height * 3 // 4
        end_x = center_x
        end_y = screen_height // 4
        slide(start_x, start_y, end_x, end_y, dur=500)
        time.sleep(3)

        # 操作3: 再次向上滑动
        print("传统方式：第二次向上滑动...")
        slide(start_x, start_y, end_x, end_y, dur=500)
        time.sleep(3)

        # 操作4: 点击点赞
        print("传统方式：点击点赞...")
        like_x = screen_width * 9 // 10
        like_y = screen_height * 2 // 3
        click(like_x, like_y)
        time.sleep(1)

        print("传统方式操作完成!")

    except Exception as e:
        print(f"传统方式操作失败: {e}")

def main():
    """主函数"""
    print("=== 抖音自动化脚本启动 ===")
    print("作者: AScript用户")
    print("功能: 启动抖音并使用HID设备进行自动化操作")
    print("=" * 50)

    # 1. 获取设备信息
    screen_width, screen_height = get_device_info()

    # 2. 启动抖音应用
    if not start_douyin():
        print("无法启动抖音应用，程序退出")
        return

    # 3. 设置HID设备
    hid_device, device_type = setup_hid_device()
    if not hid_device:
        print("无法连接HID设备，使用传统点击方式")
        print("如需使用HID功能，请确保:")
        print("1. ESP32设备已正确刷入固件")
        print("2. 蓝牙设备已配对连接 或 USB设备已授权")
        print("3. ESP32插件已正确安装")
        print("4. 参考文档: https://www.ascript.cn/docs/android/esp32")
        print()

        # 使用传统方式
        fallback_operations(screen_width, screen_height)
        print("=== 脚本执行完成（传统模式） ===")
        return

    # 4. HID设备已连接，但自动化操作功能已移除
    print("HID设备连接成功，但自动化操作功能已被移除")
    print("您可以使用其他功能：")
    print("- 测试HID连接")
    print("- 启动抖音应用")
    print("- 获取设备信息")

    print("=== 脚本执行完成 ===")

def test_hid_connection():
    """测试HID连接的独立函数"""
    print("=== HID连接测试 ===")

    hid_device, device_type = setup_hid_device()
    if hid_device:
        print(f"HID设备连接成功！类型: {device_type}")

        # 简单测试点击
        print("执行测试点击...")
        hid_device.click(100, 100, dur=50)
        print("测试完成!")
        return True
    else:
        print("HID设备连接失败!")
        return False

# ==================== UI控制器类 ====================

class DouyinUIController:
    """抖音自动化UI控制器 - 集成在主文件中"""

    def __init__(self):
        self.window = None
        self.is_running = False
        self.current_status = "待机"
        self.log_messages = []
        self.hid_device = None
        self.device_type = None
        self.screen_width = 1080
        self.screen_height = 2340
        self.page_loaded = False
        self.is_minimized = False
        self.hid_connection_thread = None
        self.task_completed = False  # 任务完成标志

    def log(self, message, level="info"):
        """添加日志消息"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        log_entry = {
            "time": timestamp,
            "level": level,
            "message": str(message)
        }
        self.log_messages.append(log_entry)

        # 保持最新的100条日志
        if len(self.log_messages) > 100:
            self.log_messages = self.log_messages[-100:]

        # 如果窗口存在且页面已加载，更新日志显示
        if self.window and self.page_loaded:
            try:
                self.window.call(f"if(typeof updateLog === 'function') updateLog({json.dumps(log_entry)})")
            except Exception as e:
                if DEBUG_MODE:
                    print(f"更新日志失败: {e}")
                pass

        print(f"[{timestamp}] {message}")

    def auto_connect_hid(self):
        """自动连接HID设备（在后台线程中运行）"""
        def connect_hid():
            try:
                self.log("正在尝试连接HID设备...", "info")
                self.update_hid_status({"connected": False, "type": "连接中...", "name": ""})

                # 尝试连接HID设备
                hid_device, device_type = setup_hid_device()

                if hid_device:
                    self.hid_device = hid_device
                    self.device_type = device_type
                    self.log(f"HID设备连接成功: {device_type}", "success")

                    # 获取设备名称
                    device_name = ""
                    try:
                        if device_type == "bluetooth" and hasattr(hid_device, 'get_name'):
                            device_name = hid_device.get_name()
                        elif device_type == "usb":
                            device_name = "USB HID设备"
                    except:
                        device_name = f"{device_type.upper()} 设备"

                    self.update_hid_status({
                        "connected": True,
                        "type": device_type,
                        "name": device_name
                    })
                else:
                    self.log("HID设备连接失败，将使用传统模式", "warning")
                    self.update_hid_status({"connected": False, "type": "未连接", "name": ""})

            except Exception as e:
                self.log(f"HID设备连接异常: {e}", "error")
                self.update_hid_status({"connected": False, "type": "连接失败", "name": ""})

        # 在后台线程中连接HID设备
        if not self.hid_connection_thread or not self.hid_connection_thread.is_alive():
            self.hid_connection_thread = threading.Thread(target=connect_hid)
            self.hid_connection_thread.daemon = True
            self.hid_connection_thread.start()

    def update_hid_status(self, device_info):
        """更新HID设备状态"""
        if self.window and self.page_loaded:
            try:
                self.window.call(f"if(typeof updateHidStatus === 'function') updateHidStatus({json.dumps(device_info)})")
            except Exception as e:
                if DEBUG_MODE:
                    print(f"更新HID状态失败: {e}")

    def update_status(self, status):
        """更新状态"""
        self.current_status = status
        if self.window and self.page_loaded:
            try:
                self.window.call(f"if(typeof updateStatus === 'function') updateStatus('{status}')")
            except Exception as e:
                if DEBUG_MODE:
                    print(f"更新状态失败: {e}")
                pass

    def tunnel_handler(self, key, value):
        """处理来自HTML的消息"""
        try:
            self.log(f"收到UI消息: {key} = {value}", "debug")

            if key == "stop_automation":
                self.stop_automation()
            elif key == "test_hid":
                self.test_hid_connection()
            elif key == "launch_douyin":
                self.launch_douyin_only()
            elif key == "get_device_info":
                self.get_device_info_ui()
            elif key == "save_config":
                self.save_config(json.loads(value))
            elif key == "load_config":
                self.load_config()
            elif key == "clear_logs":
                self.clear_logs()
            elif key == "close_window":
                self.close_window()
            elif key == "page_loaded":
                self.page_loaded = True
                self.log("页面加载完成", "info")
            elif key == "minimize_window":
                self.minimize_to_float()
            elif key == "restore_window":
                self.restore_from_float()
            elif key == "reconnect_hid":
                self.auto_connect_hid()
            else:
                self.log(f"未知命令: {key}", "warning")

        except Exception as e:
            self.log(f"处理UI消息失败: {e}", "error")



    def stop_automation(self):
        """停止自动化流程"""
        self.is_running = False
        self.update_status("停止中...")
        self.log("用户停止自动化流程", "info")
        time.sleep(1)
        self.update_status("已停止")

    def test_hid_connection(self):
        """测试HID连接"""
        self.update_status("测试HID连接...")
        self.log("开始测试HID连接")

        try:
            hid_device, device_type = setup_hid_device()
            if hid_device:
                self.log(f"HID设备连接成功: {device_type}", "success")
                self.hid_device = hid_device
                self.device_type = device_type

                # 发送设备信息到UI
                if self.window:
                    device_info = {
                        "connected": True,
                        "type": device_type,
                        "name": hid_device.get_name() if hasattr(hid_device, 'get_name') else "Unknown"
                    }
                    self.window.call(f"updateHidStatus({json.dumps(device_info)})")

                self.update_status("HID设备已连接")
            else:
                self.log("HID设备连接失败", "error")
                if self.window:
                    device_info = {"connected": False, "type": None, "name": None}
                    self.window.call(f"updateHidStatus({json.dumps(device_info)})")
                self.update_status("HID连接失败")

        except Exception as e:
            self.log(f"HID连接测试异常: {e}", "error")
            self.update_status("测试异常")

    def launch_douyin_only(self):
        """启动抖音并查找赚钱图标"""
        self.update_status("启动抖音...")
        self.log("开始启动抖音应用")

        try:
            # 先确保HID设备已连接
            if not self.hid_device:
                self.log("尝试连接HID设备...", "info")
                self.auto_connect_hid()
                # 给HID连接一些时间
                time.sleep(2)
                
                if not self.hid_device:
                    self.log("无法连接HID设备，将使用普通点击", "warning")

            # 启动抖音
            if start_douyin():
                self.log("抖音启动成功", "success")
                self.update_status("抖音已启动")

                # 启动成功后立即自动隐藏界面并显示悬浮窗
                self.log("自动隐藏界面", "info")
                self.hide_to_float()

                # 等待界面完全加载
                self.log("等待界面加载...", "info")
                time.sleep(3)

                # 查找并点击赚钱图标
                self.click_money_icon()
            else:
                self.log("抖音启动失败", "error")
                self.update_status("启动失败")
        except Exception as e:
            self.log(f"启动抖音异常: {e}", "error")
            self.update_status("启动异常")

    def click_money_icon(self):
        """点击赚钱图标"""
        try:
            self.log("开始查找赚钱图标...", "info")
            self.update_status("查找赚钱图标...")

            # 使用FindImages查找赚钱图标
            results = FindImages.find_all_template([R.img("赚钱.png"),], confidence=0.95)
            
            if results and len(results) > 0:
                self.log("找到赚钱图标，准备点击", "success")
                # 记录结果格式，便于调试
                self.log(f"图像识别结果: {results[0]}", "debug")
                self.update_status("点击赚钱图标...")

                # 解析结果获取坐标
                result = results[0]
                x, y = None, None
                
                # 根据返回的结果格式获取坐标
                if isinstance(result, dict):
                    if 'point' in result:
                        x, y = result['point']
                        self.log(f"从point字段获取坐标: ({x}, {y})", "debug")
                    elif 'rect' in result:
                        rect = result['rect']
                        x = (rect[0] + rect[2]) // 2  # 取矩形左上角和右下角x坐标的中点
                        y = (rect[1] + rect[3]) // 2  # 取矩形左上角和右下角y坐标的中点
                        self.log(f"从rect字段计算坐标: ({x}, {y})", "debug")
                    elif 'x' in result and 'y' in result:
                        x, y = result['x'], result['y']
                        self.log(f"从x,y字段获取坐标: ({x}, {y})", "debug")
                    elif 'center' in result:
                        x, y = result['center']
                        self.log(f"从center字段获取坐标: ({x}, {y})", "debug")
                    elif 'position' in result:
                        pos = result['position']
                        if isinstance(pos, (list, tuple)) and len(pos) >= 2:
                            x, y = pos[0], pos[1]
                            self.log(f"从position字段获取坐标: ({x}, {y})", "debug")
                elif hasattr(result, 'x') and hasattr(result, 'y'):
                    x, y = result.x, result.y
                    self.log(f"从对象属性获取坐标: ({x}, {y})", "debug")
                
                # 如果无法获取坐标，记录错误并返回
                if x is None or y is None:
                    self.log(f"无法从结果中提取坐标: {result}", "error")
                    self.update_status("无法获取坐标")
                    return

                # 使用HID设备点击
                if self.hid_device:
                    self.log(f"使用HID设备点击坐标: ({x}, {y})", "info")
                    self.hid_device.click(x, y, dur=50)
                    self.log("HID点击赚钱图标成功", "success")
                else:
                    # 备用方案：使用普通点击
                    self.log("HID设备不可用，使用普通点击", "warning")
                    click(x, y)
                    self.log("普通点击赚钱图标成功", "success")
                
                self.update_status("赚钱图标已点击")
                
                # 等待界面加载
                self.log("等待界面加载...", "info")
                time.sleep(3)
                
                # 查找并点击"体验双重奖励机制，一次赚到爽"控件
                self.find_and_click_reward_element()
            else:
                self.log("未找到赚钱图标", "warning")
                self.update_status("未找到赚钱图标")

        except Exception as e:
            self.log(f"点击赚钱图标失败: {e}", "error")
            self.update_status("点击赚钱图标失败")
            
    def find_and_click_reward_element(self):
        """查找并点击"体验双重奖励机制，一次赚到爽"控件"""
        try:
            self.log("开始查找奖励控件...", "info")
            self.update_status("查找奖励控件...")
            
            # 最大尝试次数
            max_attempts = 5
            attempts = 0
            
            while attempts < max_attempts:
                # 查找控件 - 首先尝试使用brother(3)定位目标元素
                base_element = Selector().desc("体验双重奖励机制，一次赚到爽").type("ViewGroup").find()
                reward_element = None
                
                if base_element:
                    self.log("找到基础元素: 体验双重奖励机制，一次赚到爽", "debug")
                    
                    # 方法1: 使用brother(3)
                    reward_element = Selector().desc("体验双重奖励机制，一次赚到爽").type("ViewGroup").brother(3).find()
                    if reward_element:
                        self.log("使用brother(3)找到目标元素", "debug")
                    else:
                        self.log("brother(3)未找到目标元素，尝试其他方法", "debug")
                        
                        # 方法2: 尝试使用parent().child()
                        try:
                            parent = base_element.parent()
                            if parent:
                                # 尝试获取父元素的第4个子元素(索引3)
                                children = parent.children()
                                if children and len(children) > 3:
                                    reward_element = children[3]
                                    self.log("使用parent().children()[3]找到目标元素", "debug")
                        except Exception as e:
                            self.log(f"尝试parent().child()方法失败: {e}", "debug")
                        
                        # 方法3: 尝试使用text包含"赚"的元素
                        if not reward_element:
                            try:
                                reward_element = Selector().textContains("赚").clickable(True).find()
                                if reward_element:
                                    self.log("使用textContains('赚')找到可点击元素", "debug")
                            except Exception as e:
                                self.log(f"尝试textContains方法失败: {e}", "debug")
                    
                    # 输出基础元素和目标元素的信息
                    try:
                        self.log(f"基础元素信息: {base_element}", "debug")
                        if reward_element:
                            self.log(f"目标元素信息: {reward_element}", "debug")
                    except:
                        pass
                else:
                    self.log("未找到基础元素", "debug")
                    
                    # 尝试直接查找可能的目标元素
                    try:
                        reward_element = Selector().textContains("赚").clickable(True).find()
                        if reward_element:
                            self.log("未找到基础元素，但使用textContains('赚')找到可点击元素", "debug")
                    except Exception as e:
                        self.log(f"尝试备用查找方法失败: {e}", "debug")
                
                if reward_element:
                    self.log("找到奖励控件，准备点击", "success")
                    self.update_status("点击奖励控件...")
                    
                    # 获取元素坐标
                    center_x = None
                    center_y = None
                    
                    try:
                        # 尝试从元素信息中获取中心点坐标
                        if isinstance(reward_element, dict) and 'center_x' in reward_element and 'center_y' in reward_element:
                            center_x = reward_element['center_x']
                            center_y = reward_element['center_y']
                            self.log(f"从字典属性获取中心点坐标: ({center_x}, {center_y})", "debug")
                        elif hasattr(reward_element, 'center_x') and hasattr(reward_element, 'center_y'):
                            center_x = reward_element.center_x
                            center_y = reward_element.center_y
                            self.log(f"从对象属性获取中心点坐标: ({center_x}, {center_y})", "debug")
                        elif 'rect' in str(reward_element):
                            # 解析rect字符串，格式通常为 Rect(x1, y1 - x2, y2)
                            rect_str = str(reward_element.get('rect', '')) if isinstance(reward_element, dict) else str(getattr(reward_element, 'rect', ''))
                            self.log(f"获取到rect字符串: {rect_str}", "debug")
                            
                            # 尝试解析rect字符串
                            import re
                            match = re.search(r'Rect\((\d+),\s*(\d+)\s*-\s*(\d+),\s*(\d+)\)', rect_str)
                            if match:
                                x1, y1, x2, y2 = map(int, match.groups())
                                center_x = (x1 + x2) // 2
                                center_y = (y1 + y2) // 2
                                self.log(f"从rect字符串解析坐标: ({center_x}, {center_y})", "debug")
                    except Exception as e:
                        self.log(f"获取坐标失败: {e}", "warning")
                    
                    # 如果仍然无法获取坐标，尝试从reward_element中提取
                    if center_x is None or center_y is None:
                        try:
                            element_info = str(reward_element)
                            self.log(f"元素完整信息: {element_info}", "debug")
                            
                            # 尝试从元素信息中提取center_x和center_y
                            import re
                            center_x_match = re.search(r"'center_x':\s*(\d+)", element_info)
                            center_y_match = re.search(r"'center_y':\s*(\d+)", element_info)
                            
                            if center_x_match and center_y_match:
                                center_x = int(center_x_match.group(1))
                                center_y = int(center_y_match.group(1))
                                self.log(f"从元素信息字符串提取坐标: ({center_x}, {center_y})", "debug")
                        except Exception as e:
                            self.log(f"从元素信息提取坐标失败: {e}", "warning")
                    
                    # 点击控件
                    if center_x is not None and center_y is not None:
                        if self.hid_device:
                            try:
                                self.log(f"使用HID设备点击坐标: ({center_x}, {center_y})", "info")
                                # 根据AScript ESP32-HID文档，正确使用click方法
                                self.hid_device.click(center_x, center_y, dur=50)
                                self.log("HID点击奖励控件成功", "success")
                            except Exception as click_error:
                                self.log(f"HID点击失败: {click_error}", "error")
                                self.log("尝试使用普通点击", "warning")
                                reward_element.click()
                                self.log("普通点击奖励控件成功", "success")
                        else:
                            self.log("HID设备不可用，使用普通点击奖励控件", "info")
                            reward_element.click()
                            self.log("普通点击奖励控件成功", "success")
                    else:
                        self.log("无法获取有效坐标，使用普通点击", "warning")
                        reward_element.click()
                        self.log("普通点击奖励控件成功", "success")
                    
                    self.update_status("奖励控件已点击")
                    
                    # 等待页面加载
                    self.log("等待页面加载...", "info")
                    time.sleep(2)
                    
                    # 开始监控金币增加状态
                    self.monitor_coin_increase()
                    
                    return True
                else:
                    attempts += 1
                    self.log(f"未找到奖励控件，尝试下滑查找 (尝试 {attempts}/{max_attempts})", "info")
                    
                    # 获取屏幕尺寸
                    width, height = self.screen_width, self.screen_height
                    if width == 0 or height == 0:
                        width, height = get_device_info()
                        self.screen_width, self.screen_height = width, height
                    
                    # 计算滑动坐标 - 添加随机化
                    import random

                    base_start_x = width // 2
                    base_start_y = int(height * 0.7)  # 屏幕70%高度位置
                    base_end_x = base_start_x
                    base_end_y = int(height * 0.3)    # 屏幕30%高度位置

                    # 添加随机偏移 (±30像素范围内)
                    random_offset_x = random.randint(-30, 30)
                    random_offset_start_y = random.randint(-20, 20)
                    random_offset_end_y = random.randint(-20, 20)

                    # 计算最终坐标
                    start_x = max(50, min(width - 50, base_start_x + random_offset_x))
                    start_y = max(int(height * 0.65), min(int(height * 0.75), base_start_y + random_offset_start_y))
                    end_x = start_x
                    end_y = max(int(height * 0.25), min(int(height * 0.35), base_end_y + random_offset_end_y))

                    # 随机化滑动持续时间 (450-550ms)
                    search_slide_duration = random.randint(450, 550)

                    # 下滑查找
                    if self.hid_device:
                        self.log(f"使用HID设备滑动屏幕: ({start_x},{start_y}) → ({end_x},{end_y}), 持续{search_slide_duration}ms", "info")
                        self.hid_device.slide(start_x, start_y, end_x, end_y, dur=search_slide_duration)
                    else:
                        self.log(f"使用普通滑动屏幕: ({start_x},{start_y}) → ({end_x},{end_y}), 持续{search_slide_duration}ms", "info")
                        slide(start_x, start_y, end_x, end_y, dur=search_slide_duration)
                    
                    # 等待滑动完成和界面刷新
                    time.sleep(1.5)
            
            self.log(f"经过{max_attempts}次尝试，仍未找到奖励控件", "warning")
            self.update_status("未找到奖励控件")
            return False
            
        except Exception as e:
            self.log(f"查找奖励控件失败: {e}", "error")
            self.update_status("查找奖励控件失败")
            return False
            
    def slide_to_next_video(self):
        """滑动到下一个视频的独立方法，提高成功率"""
        try:
            self.log("准备滑动到下一个视频", "info")
            self.update_status("滑到下一个视频...")

            # 获取屏幕尺寸
            width, height = self.screen_width, self.screen_height
            if width == 0 or height == 0:
                width, height = get_device_info()
                self.screen_width, self.screen_height = width, height

            # 记录当前视频特征，用于后续验证是否成功切换
            try:
                # 获取当前视频的用户头像控件值作为特征
                current_user_avatar = None
                current_live_text = None

                # 尝试获取用户头像控件
                try:
                    user_avatar_element = Selector().id("user_avatar").type("ImageView").packageName("com.ss.android.ugc.aweme.lite").find()
                    if user_avatar_element:
                        current_user_avatar = str(user_avatar_element)
                        self.log("记录当前用户头像控件信息", "debug")
                except Exception as e:
                    self.log(f"获取用户头像控件失败: {e}", "debug")

                # 尝试获取直播间文本控件（如果是直播）
                try:
                    live_text_element = Selector().id("ot2").type("TextView").packageName("com.ss.android.ugc.aweme.lite").find()
                    if live_text_element:
                        current_live_text = str(live_text_element)
                        self.log("记录当前直播间文本控件信息", "debug")
                except Exception as e:
                    self.log(f"获取直播间文本控件失败: {e}", "debug")

                self.log(f"记录视频特征 - 用户头像: {'有' if current_user_avatar else '无'}, 直播文本: {'有' if current_live_text else '无'}", "debug")
            except Exception as e:
                self.log(f"记录视频特征失败: {e}", "warning")
                current_user_avatar = None
                current_live_text = None

            # 滑动到下一个视频，最多尝试3次
            max_slide_attempts = 3
            slide_success = False

            for attempt in range(1, max_slide_attempts + 1):
                self.log(f"尝试滑动到下一个视频 (第{attempt}/{max_slide_attempts}次)", "info")

                # 计算滑动坐标 (从下到上滑动，切换到下一个视频) - 添加随机化避免检测
                import random

                # 基础坐标
                base_start_x = width // 2
                base_start_y = int(height * 0.8)  # 从屏幕80%高度位置开始
                base_end_x = base_start_x
                base_end_y = int(height * 0.2)    # 滑动到屏幕20%高度位置

                # 添加随机偏移 (±50像素范围内)
                random_offset_x = random.randint(-50, 50)
                random_offset_start_y = random.randint(-30, 30)
                random_offset_end_y = random.randint(-30, 30)

                # 计算最终坐标
                start_x = max(50, min(width - 50, base_start_x + random_offset_x))  # 确保不超出屏幕边界
                start_y = max(int(height * 0.75), min(int(height * 0.85), base_start_y + random_offset_start_y))
                end_x = start_x  # 保持X坐标一致，只是垂直滑动
                end_y = max(int(height * 0.15), min(int(height * 0.25), base_end_y + random_offset_end_y))

                # 随机化滑动持续时间 (700-900ms)
                slide_duration = random.randint(700, 900)

                # 执行滑动操作
                if self.hid_device:
                    self.log(f"使用HID设备滑动: ({start_x},{start_y}) → ({end_x},{end_y}), 持续{slide_duration}ms", "info")
                    try:
                        self.hid_device.slide(start_x, start_y, end_x, end_y, dur=slide_duration)
                    except Exception as slide_e:
                        self.log(f"HID滑动失败: {slide_e}，尝试普通滑动", "warning")
                        slide(start_x, start_y, end_x, end_y, dur=slide_duration)
                else:
                    self.log(f"使用普通滑动: ({start_x},{start_y}) → ({end_x},{end_y}), 持续{slide_duration}ms", "info")
                    slide(start_x, start_y, end_x, end_y, dur=slide_duration)

                # 等待视频加载和切换完成
                self.log("等待视频切换...", "info")
                time.sleep(3)  # 增加等待时间，确保视频完全加载

                # 滑动后立即检测"回任务页"控件
                if self.check_and_click_back_to_task():
                    # 如果找到并点击了"回任务页"控件，说明任务完成
                    return True

                # 验证是否成功切换到新视频
                try:
                    # 检查控件变化来判断视频是否切换
                    video_switched = False

                    # 获取当前的用户头像控件
                    try:
                        new_user_avatar_element = Selector().id("user_avatar").type("ImageView").packageName("com.ss.android.ugc.aweme.lite").find()
                        new_user_avatar = str(new_user_avatar_element) if new_user_avatar_element else None

                        if current_user_avatar and new_user_avatar:
                            if current_user_avatar != new_user_avatar:
                                self.log("用户头像控件发生变化，视频切换成功", "success")
                                video_switched = True
                            else:
                                self.log("用户头像控件未变化，可能未成功切换视频", "warning")
                        elif current_user_avatar is None and new_user_avatar:
                            self.log("检测到新的用户头像控件，视频切换成功", "success")
                            video_switched = True
                        elif current_user_avatar and new_user_avatar is None:
                            self.log("用户头像控件消失，可能切换到直播间", "info")
                            video_switched = True
                    except Exception as avatar_e:
                        self.log(f"检测用户头像控件变化失败: {avatar_e}", "debug")

                    # 如果用户头像检测失败，尝试检测直播间文本控件
                    if not video_switched:
                        try:
                            new_live_text_element = Selector().id("ot2").type("TextView").packageName("com.ss.android.ugc.aweme.lite").find()
                            new_live_text = str(new_live_text_element) if new_live_text_element else None

                            if current_live_text and new_live_text:
                                if current_live_text != new_live_text:
                                    self.log("直播间文本控件发生变化，视频切换成功", "success")
                                    video_switched = True
                                else:
                                    self.log("直播间文本控件未变化，可能未成功切换视频", "warning")
                            elif current_live_text is None and new_live_text:
                                self.log("检测到新的直播间文本控件，切换到直播间", "success")
                                video_switched = True
                            elif current_live_text and new_live_text is None:
                                self.log("直播间文本控件消失，可能切换到普通视频", "info")
                                video_switched = True
                        except Exception as live_e:
                            self.log(f"检测直播间文本控件变化失败: {live_e}", "debug")

                    if video_switched:
                        self.log("检测到界面控件变化，视频切换成功", "success")
                        slide_success = True
                        break
                    else:
                        self.log("未检测到明显的控件变化，可能未成功切换视频", "warning")

                except Exception as verify_e:
                    self.log(f"验证视频切换失败: {verify_e}", "warning")

                # 如果是最后一次尝试，不需要再等待
                if attempt < max_slide_attempts:
                    self.log("准备再次尝试滑动...", "info")
                    time.sleep(1)

            if slide_success:
                self.log("已成功滑到下一个视频", "success")
            else:
                # 尝试使用更强力的滑动方式
                self.log("常规滑动失败，尝试使用强力滑动...", "warning")

                # 使用更长的滑动距离和更慢的滑动速度 - 添加随机化
                base_start_x = width // 2
                base_start_y = int(height * 0.9)  # 从屏幕90%高度位置开始
                base_end_x = base_start_x
                base_end_y = int(height * 0.1)    # 滑动到屏幕10%高度位置

                # 强力滑动的随机偏移 (±40像素范围内)
                random_offset_x = random.randint(-40, 40)
                random_offset_start_y = random.randint(-20, 20)
                random_offset_end_y = random.randint(-20, 20)

                # 计算强力滑动坐标
                start_x = max(50, min(width - 50, base_start_x + random_offset_x))
                start_y = max(int(height * 0.85), min(int(height * 0.95), base_start_y + random_offset_start_y))
                end_x = start_x
                end_y = max(int(height * 0.05), min(int(height * 0.15), base_end_y + random_offset_end_y))

                # 随机化强力滑动持续时间 (1100-1300ms)
                strong_slide_duration = random.randint(1100, 1300)

                # 执行强力滑动
                try:
                    if self.hid_device:
                        self.log(f"使用HID设备执行强力滑动: ({start_x},{start_y}) → ({end_x},{end_y}), 持续{strong_slide_duration}ms", "info")
                        self.hid_device.slide(start_x, start_y, end_x, end_y, dur=strong_slide_duration)
                    else:
                        self.log(f"使用普通方式执行强力滑动: ({start_x},{start_y}) → ({end_x},{end_y}), 持续{strong_slide_duration}ms", "info")
                        slide(start_x, start_y, end_x, end_y, dur=strong_slide_duration)

                    self.log("强力滑动执行完成", "info")

                    # 强力滑动后再次检测"回任务页"控件
                    time.sleep(2)  # 等待界面稳定
                    if self.check_and_click_back_to_task():
                        return True

                except Exception as e:
                    self.log(f"强力滑动失败: {e}", "error")

            self.update_status("已切换视频")
            return True

        except Exception as e:
            self.log(f"滑动到下一个视频失败: {e}", "error")
            self.update_status("滑动失败")
            return False

    def check_and_click_back_to_task(self):
        """检测并点击"回任务页"控件，如果找到说明已完成所有滑动"""
        try:
            self.log("检测是否存在'回任务页'控件...", "info")
            self.update_status("检测回任务页控件...")

            # 使用指定的Selector查找"回任务页"控件
            back_to_task_element = Selector().id("90").text("回任务页").type("TextView").packageName("com.ss.android.ugc.aweme.lite").find()

            if back_to_task_element:
                self.log("🎉 发现'回任务页'控件，说明已完成所有滑动任务！", "success")
                self.update_status("发现回任务页控件")

                # 点击"回任务页"控件
                try:
                    # 尝试多种方式获取控件坐标并点击
                    click_success = False

                    if self.hid_device:
                        # 方法1: 尝试获取控件坐标用于HID点击
                        try:
                            center_x, center_y = None, None

                            # 优先尝试直接从控件字典中获取center_x和center_y
                            if isinstance(back_to_task_element, dict):
                                if 'center_x' in back_to_task_element and 'center_y' in back_to_task_element:
                                    center_x = back_to_task_element['center_x']
                                    center_y = back_to_task_element['center_y']
                                    self.log(f"从控件字典获取坐标: ({center_x}, {center_y})", "debug")
                            elif hasattr(back_to_task_element, 'center_x') and hasattr(back_to_task_element, 'center_y'):
                                center_x = back_to_task_element.center_x
                                center_y = back_to_task_element.center_y
                                self.log(f"从控件属性获取坐标: ({center_x}, {center_y})", "debug")

                            # 如果没有直接的center坐标，尝试其他方式
                            if center_x is None:
                                # 尝试bounds属性
                                if hasattr(back_to_task_element, 'bounds'):
                                    try:
                                        bounds = back_to_task_element.bounds
                                        # bounds可能是属性而不是方法
                                        if callable(bounds):
                                            bounds = bounds()

                                        if bounds and hasattr(bounds, 'left'):
                                            center_x = (bounds.left + bounds.right) // 2
                                            center_y = (bounds.top + bounds.bottom) // 2
                                            self.log(f"通过bounds属性获取坐标: ({center_x}, {center_y})", "debug")
                                    except Exception as bounds_e:
                                        self.log(f"获取bounds失败: {bounds_e}", "debug")

                                # 尝试从rect字符串解析坐标
                                if center_x is None and hasattr(back_to_task_element, 'rect'):
                                    try:
                                        rect = back_to_task_element.rect
                                        rect_str = str(rect)
                                        self.log(f"rect字符串: {rect_str}", "debug")

                                        # 解析类似 "Rect(489, 2061 - 653, 2116)" 的字符串
                                        import re
                                        rect_match = re.search(r'Rect\((\d+),\s*(\d+)\s*-\s*(\d+),\s*(\d+)\)', rect_str)
                                        if rect_match:
                                            x1, y1, x2, y2 = map(int, rect_match.groups())
                                            center_x = (x1 + x2) // 2
                                            center_y = (y1 + y2) // 2
                                            self.log(f"通过rect字符串解析获取坐标: ({center_x}, {center_y})", "debug")
                                    except Exception as rect_e:
                                        self.log(f"获取rect失败: {rect_e}", "debug")

                            if center_x is not None and center_y is not None:
                                self.log(f"使用HID设备点击'回任务页'控件坐标: ({center_x}, {center_y})", "info")
                                # 根据AScript ESP32-HID文档，使用正确的点击方法
                                self.hid_device.click(center_x, center_y, dur=50)
                                self.log("HID点击'回任务页'控件成功", "success")
                                click_success = True
                            else:
                                self.log("无法获取控件坐标，尝试其他方式", "warning")

                        except Exception as hid_e:
                            self.log(f"HID点击失败: {hid_e}", "warning")

                    # 方法2: 如果HID点击失败，尝试使用AScript的click函数
                    if not click_success:
                        try:
                            # 尝试获取控件中心坐标用于click函数
                            center_x, center_y = None, None

                            # 尝试多种方式获取坐标（与HID方式相同）
                            if hasattr(back_to_task_element, 'bounds'):
                                try:
                                    bounds = back_to_task_element.bounds
                                    if callable(bounds):
                                        bounds = bounds()

                                    if bounds and hasattr(bounds, 'left'):
                                        center_x = (bounds.left + bounds.right) // 2
                                        center_y = (bounds.top + bounds.bottom) // 2
                                except:
                                    pass

                            # 尝试通过字符串解析获取坐标信息
                            if center_x is None:
                                try:
                                    element_str = str(back_to_task_element)
                                    self.log(f"元素信息: {element_str}", "debug")

                                    # 尝试从字符串中解析坐标信息
                                    import re
                                    # 查找类似 "bounds": [x1, y1, x2, y2] 的模式
                                    bounds_match = re.search(r'"bounds":\s*\[(\d+),\s*(\d+),\s*(\d+),\s*(\d+)\]', element_str)
                                    if bounds_match:
                                        x1, y1, x2, y2 = map(int, bounds_match.groups())
                                        center_x = (x1 + x2) // 2
                                        center_y = (y1 + y2) // 2
                                        self.log(f"通过字符串解析获取坐标: ({center_x}, {center_y})", "debug")
                                except Exception as parse_e:
                                    self.log(f"字符串解析失败: {parse_e}", "debug")

                            if center_x is not None and center_y is not None:
                                self.log(f"使用AScript click函数点击坐标: ({center_x}, {center_y})", "info")
                                click(center_x, center_y)
                                self.log("AScript点击'回任务页'控件成功", "success")
                                click_success = True
                            else:
                                self.log("无法获取坐标，尝试元素点击方法", "warning")

                        except Exception as click_e:
                            self.log(f"AScript点击失败: {click_e}", "warning")

                    # 方法3: 最后尝试元素的点击方法（如果支持）
                    if not click_success:
                        try:
                            if hasattr(back_to_task_element, 'click'):
                                back_to_task_element.click()
                                self.log("元素点击'回任务页'控件成功", "success")
                                click_success = True
                            else:
                                self.log("控件不支持click方法", "warning")
                        except Exception as elem_e:
                            self.log(f"元素点击失败: {elem_e}", "warning")

                    if not click_success:
                        self.log("所有点击方式都失败，但已找到'回任务页'控件", "warning")
                        # 即使点击失败，也认为任务完成，因为已经检测到了控件
                        click_success = True

                    if click_success:
                        # 更新状态并提示完成
                        self.update_status("✅ 任务完成！已点击回任务页")
                        self.log("🎊 恭喜！所有滑动任务已完成，已成功点击'回任务页'按钮", "success")

                        # 等待界面跳转
                        time.sleep(2)

                        # 执行后续操作
                        if self.execute_post_task_operations():
                            self.log("✅ 后续操作执行完成", "success")

                            # 开始第二轮操作循环
                            self.log("🔄 开始第二轮操作循环", "info")
                            self.update_status("开始第二轮循环...")

                            # 重新开始查找赚钱图标和监控流程
                            self.start_second_round()

                        else:
                            self.log("⚠️  后续操作执行失败，但主要任务已完成", "warning")
                            # 显示完成提示
                            self.show_completion_message()
                            # 设置全局完成标志，停止所有后续操作
                            self.task_completed = True

                        return True
                    else:
                        # 即使点击失败，也认为任务完成，因为已经检测到了"回任务页"控件
                        self.update_status("✅ 任务完成！已检测到回任务页")
                        self.log("🎊 恭喜！所有滑动任务已完成，已检测到'回任务页'控件", "success")
                        self.log("⚠️  点击操作失败，但任务已完成", "warning")

                        # 尝试执行后续操作（即使点击失败）
                        if self.execute_post_task_operations():
                            self.log("✅ 后续操作执行完成", "success")
                        else:
                            self.log("⚠️  后续操作执行失败", "warning")

                        # 显示完成提示
                        self.show_completion_message()

                        # 设置全局完成标志，停止所有后续操作
                        self.task_completed = True

                        return True

                except Exception as click_e:
                    self.log(f"处理'回任务页'控件时发生错误: {click_e}", "error")
                    # 即使出现错误，也认为任务完成，因为已经检测到了控件
                    self.update_status("✅ 任务完成！已检测到回任务页")
                    self.log("🎊 虽然处理过程中出现错误，但已检测到'回任务页'控件，任务完成", "success")

                    # 尝试执行后续操作（即使出现错误）
                    try:
                        if self.execute_post_task_operations():
                            self.log("✅ 后续操作执行完成", "success")
                        else:
                            self.log("⚠️  后续操作执行失败", "warning")
                    except Exception as post_e:
                        self.log(f"后续操作执行异常: {post_e}", "error")

                    # 显示完成提示
                    self.show_completion_message()

                    # 设置全局完成标志，停止所有后续操作
                    self.task_completed = True

                    return True
            else:
                # 降低日志级别，避免频繁输出
                # self.log("未找到'回任务页'控件，继续滑动", "debug")
                return False

        except Exception as e:
            self.log(f"检测'回任务页'控件失败: {e}", "error")
            return False

    def execute_post_task_operations(self):
        """执行点击回任务页后的后续操作"""
        try:
            self.log("开始执行后续操作...", "info")
            self.update_status("执行后续操作...")

            # 步骤1: 点击返回控件
            self.log("步骤1: 点击返回控件", "info")
            try:
                # 查找返回控件
                back_control = Selector().type("ImageView").path("FrameLayout/LinearLayout/FrameLayout/FrameLayout/FrameLayout/FrameLayout/ViewGroup/TabHost/FrameLayout/FrameLayout/RelativeLayout/FrameLayout/FrameLayout/FrameLayout/FrameLayout/FrameLayout/FrameLayout/ViewGroup/ViewGroup/ViewGroup/ImageView").find()

                if back_control:
                    self.log("找到返回控件", "success")

                    # 使用HID点击返回控件
                    if self.hid_device:
                        click_success = self.click_element_with_hid_only(back_control, "返回控件")

                        if click_success:
                            self.log("HID点击返回控件成功", "success")

                            # 等待界面响应
                            time.sleep(2)

                        else:
                            self.log("HID点击返回控件失败，尝试备用方案", "warning")
                            # 备用方案：使用HID返回键
                            try:
                                self.hid_device.back()
                                self.log("HID返回键操作执行成功（备用方案）", "success")
                                time.sleep(2)
                            except Exception as hid_back_e:
                                self.log(f"HID返回键操作失败: {hid_back_e}", "error")
                                return False
                    else:
                        self.log("没有HID设备，无法点击返回控件", "error")
                        # 备用方案：使用普通返回
                        try:
                            from ascript.android.action import back
                            back()
                            self.log("普通返回操作执行成功（备用方案）", "success")
                            time.sleep(2)
                        except ImportError:
                            # 如果导入失败，使用系统返回
                            system.back()
                            self.log("系统返回操作执行成功（备用方案）", "success")
                            time.sleep(2)
                else:
                    self.log("未找到返回控件，使用备用返回方案", "warning")

                    # 备用方案：使用HID返回键
                    if self.hid_device:
                        try:
                            self.hid_device.back()
                            self.log("HID返回键操作执行成功（备用方案）", "success")
                            time.sleep(2)
                        except Exception as hid_back_e:
                            self.log(f"HID返回键操作失败: {hid_back_e}", "error")
                            return False
                    else:
                        # 使用普通返回
                        try:
                            from ascript.android.action import back
                            back()
                            self.log("普通返回操作执行成功（备用方案）", "success")
                            time.sleep(2)
                        except ImportError:
                            # 如果导入失败，使用系统返回
                            system.back()
                            self.log("系统返回操作执行成功（备用方案）", "success")
                            time.sleep(2)

            except Exception as back_e:
                self.log(f"返回操作失败: {back_e}", "error")
                return False

            # 步骤2: 查找并点击 content_container 控件
            self.log("步骤2: 查找并点击 content_container 控件", "info")
            try:
                content_container = Selector().id("content_container").type("FrameLayout").packageName("com.ss.android.ugc.aweme.lite").find()

                if content_container:
                    self.log("找到 content_container 控件", "success")

                    # 点击控件
                    click_success = self.click_element_with_multiple_methods(content_container, "content_container")

                    if click_success:
                        self.log("content_container 控件点击成功", "success")
                        # 等待界面响应
                        time.sleep(2)
                    else:
                        self.log("content_container 控件点击失败", "error")
                        return False
                else:
                    self.log("未找到 content_container 控件", "error")
                    return False

            except Exception as content_e:
                self.log(f"处理 content_container 控件失败: {content_e}", "error")
                return False

            # 步骤3: 查找并点击 fl_intput_hint_container 控件，获取焦点并输入文本
            self.log("步骤3: 查找并点击 fl_intput_hint_container 控件", "info")
            try:
                input_container = Selector().id("fl_intput_hint_container").type("FrameLayout").packageName("com.ss.android.ugc.aweme.lite").find()

                if input_container:
                    self.log("找到 fl_intput_hint_container 控件", "success")

                    # 点击控件获取焦点
                    click_success = self.click_element_with_multiple_methods(input_container, "fl_intput_hint_container")

                    if click_success:
                        self.log("fl_intput_hint_container 控件点击成功，获取焦点", "success")

                        # 等待输入框获取焦点
                        time.sleep(1)

                        # 输入文本 "央视新闻"
                        self.log("开始输入文本: 央视新闻", "info")
                        try:
                            # 使用AScript的IME输入法
                            from ascript.android.action import Ime

                            # 检查IME输入法是否可用
                            if Ime.is_active():
                                self.log("AScript输入法已激活，使用IME输入", "info")
                                # 先清空输入框
                                Ime.input_clear()
                                time.sleep(0.5)
                                # 输入文本
                                Ime.input("央视新闻")
                                self.log("IME文本输入成功: 央视新闻", "success")
                            else:
                                self.log("AScript输入法未激活，使用普通输入方法", "warning")
                                # 使用普通输入方法作为备用
                                try:
                                    # 尝试使用系统输入
                                    system.input_text("央视新闻")
                                    self.log("系统文本输入成功: 央视新闻", "success")
                                except:
                                    self.log("系统文本输入失败，跳过输入步骤", "warning")

                            # 等待输入完成
                            time.sleep(1)

                        except Exception as input_e:
                            self.log(f"文本输入失败: {input_e}", "error")
                            return False

                    else:
                        self.log("fl_intput_hint_container 控件点击失败", "error")
                        return False
                else:
                    self.log("未找到 fl_intput_hint_container 控件", "error")
                    return False

            except Exception as input_e:
                self.log(f"处理 fl_intput_hint_container 控件失败: {input_e}", "error")
                return False

            # 步骤4: 查找并点击搜索按钮
            self.log("步骤4: 查找并点击搜索按钮", "info")
            try:
                search_button = Selector().id("nsl").text("搜索").type("TextView").packageName("com.ss.android.ugc.aweme.lite").find()

                if search_button:
                    self.log("找到搜索按钮", "success")

                    # 点击搜索按钮
                    click_success = self.click_element_with_multiple_methods(search_button, "搜索按钮")

                    if click_success:
                        self.log("搜索按钮点击成功", "success")

                        # 等待搜索结果加载
                        time.sleep(2)

                    else:
                        self.log("搜索按钮点击失败", "error")
                        return False
                else:
                    self.log("未找到搜索按钮", "error")
                    return False

            except Exception as search_e:
                self.log(f"处理搜索按钮失败: {search_e}", "error")
                return False

            # 步骤5: 点击"直播"控件
            self.log("步骤5: 查找并点击'直播'控件", "info")
            try:
                live_button = Selector().id("text1").text("直播").type("TextView").packageName("android").find()

                if live_button:
                    self.log("找到'直播'控件", "success")

                    # 点击直播控件
                    click_success = self.click_element_with_multiple_methods(live_button, "直播控件")

                    if click_success:
                        self.log("'直播'控件点击成功", "success")

                        # 等待直播页面加载
                        time.sleep(3)

                    else:
                        self.log("'直播'控件点击失败", "error")
                        return False
                else:
                    self.log("未找到'直播'控件", "error")
                    return False

            except Exception as live_e:
                self.log(f"处理'直播'控件失败: {live_e}", "error")
                return False

            # 步骤6: 查找直播中图片并HID点击
            self.log("步骤6: 查找直播中图片并HID点击", "info")
            try:
                # 使用FindImages查找直播中图片
                results = FindImages.find_all_template([R.img("直播中.png")], confidence=0.95)

                if results and len(results) > 0:
                    self.log("找到直播中图片，准备点击第一个", "success")
                    # 记录结果格式，便于调试
                    self.log(f"图像识别结果: {results[0]}", "debug")

                    # 解析结果获取坐标
                    result = results[0]
                    x, y = None, None

                    # 尝试多种方式解析坐标
                    if isinstance(result, dict):
                        # 如果结果是字典格式
                        if 'x' in result and 'y' in result:
                            x, y = result['x'], result['y']
                        elif 'center_x' in result and 'center_y' in result:
                            x, y = result['center_x'], result['center_y']
                        elif 'left' in result and 'top' in result and 'right' in result and 'bottom' in result:
                            x = (result['left'] + result['right']) // 2
                            y = (result['top'] + result['bottom']) // 2
                    elif isinstance(result, (list, tuple)) and len(result) >= 2:
                        # 如果结果是列表或元组格式
                        x, y = result[0], result[1]
                    elif hasattr(result, 'x') and hasattr(result, 'y'):
                        # 如果结果是对象格式
                        x, y = result.x, result.y
                    elif hasattr(result, 'center_x') and hasattr(result, 'center_y'):
                        x, y = result.center_x, result.center_y

                    if x is not None and y is not None:
                        self.log(f"解析到直播中图片坐标: ({x}, {y})", "success")

                        # 使用HID点击直播中图片
                        if self.hid_device:
                            try:
                                self.hid_device.click(x, y, dur=50)
                                self.log("HID点击直播中图片成功", "success")

                                # 等待进入直播间
                                time.sleep(3)

                            except Exception as hid_e:
                                self.log(f"HID点击直播中图片失败: {hid_e}", "error")
                                return False
                        else:
                            self.log("没有HID设备，无法点击直播中图片", "error")
                            return False
                    else:
                        self.log("无法解析直播中图片坐标", "error")
                        return False
                else:
                    self.log("未找到直播中图片", "warning")

                    # 备用方案：尝试原来的直播区域控件
                    self.log("尝试备用方案：查找直播区域控件", "info")
                    live_area = Selector().id("top_mask_without_gradient").type("View").packageName("com.ss.android.ugc.aweme.search_lite_plugin").parent(1).find()

                    if live_area:
                        self.log("找到直播区域控件（备用方案）", "success")

                        # 使用HID点击直播区域
                        if self.hid_device:
                            click_success = self.click_element_with_hid_only(live_area, "直播区域")

                            if click_success:
                                self.log("HID点击直播区域成功（备用方案）", "success")
                                # 等待进入直播间
                                time.sleep(3)
                            else:
                                self.log("HID点击直播区域失败（备用方案）", "error")
                                return False
                        else:
                            self.log("没有HID设备，无法点击直播区域", "error")
                            return False
                    else:
                        self.log("未找到直播区域控件（备用方案）", "error")
                        return False

            except Exception as area_e:
                self.log(f"处理直播中图片失败: {area_e}", "error")
                return False

            # 步骤7: 观看直播15-20分钟
            self.log("步骤7: 开始观看直播15-20分钟", "info")
            try:
                import random
                # 随机观看时间 15-20分钟
                watch_duration = random.randint(15, 20)
                self.log(f"将观看直播 {watch_duration} 分钟", "info")
                self.update_status(f"观看直播中... ({watch_duration}分钟)")

                # 转换为秒
                watch_seconds = watch_duration * 60
                start_time = time.time()

                # 每分钟更新一次状态
                while time.time() - start_time < watch_seconds:
                    elapsed_minutes = int((time.time() - start_time) / 60)
                    remaining_minutes = watch_duration - elapsed_minutes

                    if remaining_minutes > 0:
                        self.update_status(f"观看直播中... (剩余{remaining_minutes}分钟)")
                        self.log(f"已观看 {elapsed_minutes} 分钟，剩余 {remaining_minutes} 分钟", "info")

                    # 每分钟检查一次
                    time.sleep(60)

                self.log(f"直播观看完成，共观看了 {watch_duration} 分钟", "success")

            except Exception as watch_e:
                self.log(f"观看直播过程中出现错误: {watch_e}", "error")
                # 即使观看过程出错，也继续后续操作

            # 步骤8: HID点击关闭直播
            self.log("步骤8: HID点击关闭直播", "info")
            try:
                close_button = Selector().id("bk=").desc("关闭").type("ImageView").packageName("com.ss.android.ugc.aweme.lite").find()

                if close_button:
                    self.log("找到关闭直播按钮", "success")

                    # 使用HID点击关闭按钮
                    if self.hid_device:
                        click_success = self.click_element_with_hid_only(close_button, "关闭直播按钮")

                        if click_success:
                            self.log("HID点击关闭直播按钮成功", "success")

                            # 等待直播关闭
                            time.sleep(2)

                        else:
                            self.log("HID点击关闭直播按钮失败", "error")
                            # 继续执行，不返回False
                    else:
                        self.log("没有HID设备，无法点击关闭按钮", "error")
                        # 继续执行，不返回False
                else:
                    self.log("未找到关闭直播按钮", "warning")
                    # 继续执行，不返回False

            except Exception as close_e:
                self.log(f"处理关闭直播按钮失败: {close_e}", "error")
                # 继续执行，不返回False

            # 步骤9: 点击返回按钮到开始查找赚钱图标
            self.log("步骤9: 点击返回按钮到开始查找赚钱图标", "info")

            # 使用重试机制确保返回成功并能找到赚钱图标
            max_retry_attempts = 3
            retry_success = False

            for retry_attempt in range(max_retry_attempts):
                try:
                    self.log(f"返回操作尝试 {retry_attempt + 1}/{max_retry_attempts}", "info")

                    # 使用FindImages查找返回按钮图片
                    results = FindImages.find_all_template([R.img("返回.png")], confidence=0.95)

                    if results and len(results) > 0:
                        self.log("找到返回按钮图片，准备点击第一个", "success")
                        # 记录结果格式，便于调试
                        self.log(f"图像识别结果: {results[0]}", "debug")

                        # 解析结果获取坐标
                        result = results[0]
                        x, y = None, None

                        # 尝试多种方式解析坐标
                        if isinstance(result, dict):
                            # 如果结果是字典格式
                            if 'x' in result and 'y' in result:
                                x, y = result['x'], result['y']
                            elif 'center_x' in result and 'center_y' in result:
                                x, y = result['center_x'], result['center_y']
                            elif 'left' in result and 'top' in result and 'right' in result and 'bottom' in result:
                                x = (result['left'] + result['right']) // 2
                                y = (result['top'] + result['bottom']) // 2
                        elif isinstance(result, (list, tuple)) and len(result) >= 2:
                            # 如果结果是列表或元组格式
                            x, y = result[0], result[1]
                        elif hasattr(result, 'x') and hasattr(result, 'y'):
                            # 如果结果是对象格式
                            x, y = result.x, result.y
                        elif hasattr(result, 'center_x') and hasattr(result, 'center_y'):
                            x, y = result.center_x, result.center_y

                        if x is not None and y is not None:
                            self.log(f"解析到返回按钮坐标: ({x}, {y})", "success")

                            # 使用HID点击返回按钮
                            if self.hid_device:
                                try:
                                    self.hid_device.click(x, y, dur=50)
                                    self.log("HID点击返回按钮成功", "success")

                                    # 等待界面响应
                                    time.sleep(3)

                                    # 检查是否成功返回并能找到赚钱图标
                                    if self.check_money_icon_available():
                                        self.log("返回成功，找到赚钱图标", "success")
                                        retry_success = True
                                        break
                                    else:
                                        self.log(f"返回后未找到赚钱图标，尝试重试 ({retry_attempt + 1}/{max_retry_attempts})", "warning")

                                except Exception as hid_e:
                                    self.log(f"HID点击返回按钮失败: {hid_e}", "error")
                            else:
                                self.log("没有HID设备，无法点击返回按钮", "error")
                        else:
                            self.log("无法解析返回按钮坐标", "error")
                    else:
                        self.log("未找到返回按钮图片，尝试备用方案", "warning")

                        # 备用方案1：尝试原来的控件查找方式
                        back_button = Selector().id("back_btn_left").desc("返回").type("ImageView").packageName("com.ss.android.ugc.aweme.search_lite_plugin").find()

                        if back_button:
                            self.log("找到返回按钮控件（备用方案）", "success")

                            # 使用HID点击返回按钮
                            if self.hid_device:
                                click_success = self.click_element_with_hid_only(back_button, "返回按钮")

                                if click_success:
                                    self.log("HID点击返回按钮成功（备用方案）", "success")

                                    # 等待界面响应
                                    time.sleep(3)

                                    # 检查是否成功返回并能找到赚钱图标
                                    if self.check_money_icon_available():
                                        self.log("返回成功，找到赚钱图标（备用方案）", "success")
                                        retry_success = True
                                        break
                                    else:
                                        self.log(f"返回后未找到赚钱图标，尝试重试 ({retry_attempt + 1}/{max_retry_attempts})", "warning")
                                else:
                                    self.log("HID点击返回按钮失败（备用方案）", "error")
                            else:
                                self.log("没有HID设备，无法点击返回按钮（备用方案）", "error")
                        else:
                            self.log("未找到返回按钮控件，使用HID返回键", "warning")

                            # 备用方案2：使用HID返回键
                            if self.hid_device:
                                try:
                                    self.hid_device.back()
                                    self.log("HID返回键操作执行成功", "success")
                                    time.sleep(3)

                                    # 检查是否成功返回并能找到赚钱图标
                                    if self.check_money_icon_available():
                                        self.log("返回成功，找到赚钱图标（HID返回键）", "success")
                                        retry_success = True
                                        break
                                    else:
                                        self.log(f"返回后未找到赚钱图标，尝试重试 ({retry_attempt + 1}/{max_retry_attempts})", "warning")

                                except Exception as hid_back_e:
                                    self.log(f"HID返回键操作失败: {hid_back_e}", "error")
                            else:
                                # 备用方案3：使用普通返回
                                try:
                                    system.back()
                                    self.log("系统返回操作执行成功", "success")
                                    time.sleep(3)

                                    # 检查是否成功返回并能找到赚钱图标
                                    if self.check_money_icon_available():
                                        self.log("返回成功，找到赚钱图标（系统返回）", "success")
                                        retry_success = True
                                        break
                                    else:
                                        self.log(f"返回后未找到赚钱图标，尝试重试 ({retry_attempt + 1}/{max_retry_attempts})", "warning")

                                except Exception as sys_back_e:
                                    self.log(f"系统返回操作失败: {sys_back_e}", "error")

                    # 如果这次尝试失败，等待一下再重试
                    if not retry_success and retry_attempt < max_retry_attempts - 1:
                        self.log("等待2秒后重试...", "info")
                        time.sleep(2)

                except Exception as back_e:
                    self.log(f"返回操作尝试 {retry_attempt + 1} 失败: {back_e}", "error")
                    if retry_attempt < max_retry_attempts - 1:
                        self.log("等待2秒后重试...", "info")
                        time.sleep(2)

            if not retry_success:
                self.log("所有返回尝试都失败，无法找到赚钱图标", "error")
                # 即使失败也继续执行，不返回False

            self.log("🎉 所有后续操作执行完成！", "success")
            self.log("🔄 准备开始第二轮操作循环", "info")
            self.update_status("后续操作完成，准备第二轮循环")
            return True

        except Exception as e:
            self.log(f"执行后续操作失败: {e}", "error")
            self.update_status("后续操作失败")
            return False

    def click_element_with_multiple_methods(self, element, element_name):
        """使用多种方法点击控件的通用方法"""
        try:
            click_success = False

            if self.hid_device:
                # 方法1: 尝试HID点击
                try:
                    center_x, center_y = None, None

                    # 优先尝试直接从控件字典中获取center_x和center_y
                    if isinstance(element, dict):
                        if 'center_x' in element and 'center_y' in element:
                            center_x = element['center_x']
                            center_y = element['center_y']
                            self.log(f"从{element_name}控件字典获取坐标: ({center_x}, {center_y})", "debug")
                    elif hasattr(element, 'center_x') and hasattr(element, 'center_y'):
                        center_x = element.center_x
                        center_y = element.center_y
                        self.log(f"从{element_name}控件属性获取坐标: ({center_x}, {center_y})", "debug")

                    # 如果没有直接的center坐标，尝试其他方式
                    if center_x is None:
                        # 尝试bounds属性
                        if hasattr(element, 'bounds'):
                            try:
                                bounds = element.bounds
                                if callable(bounds):
                                    bounds = bounds()

                                if bounds and hasattr(bounds, 'left'):
                                    center_x = (bounds.left + bounds.right) // 2
                                    center_y = (bounds.top + bounds.bottom) // 2
                                    self.log(f"通过{element_name}控件bounds属性获取坐标: ({center_x}, {center_y})", "debug")
                            except Exception as bounds_e:
                                self.log(f"获取{element_name}控件bounds失败: {bounds_e}", "debug")

                        # 尝试从rect字符串解析坐标
                        if center_x is None and hasattr(element, 'rect'):
                            try:
                                rect = element.rect
                                rect_str = str(rect)
                                self.log(f"{element_name}控件rect字符串: {rect_str}", "debug")

                                # 解析类似 "Rect(489, 2061 - 653, 2116)" 的字符串
                                import re
                                rect_match = re.search(r'Rect\((\d+),\s*(\d+)\s*-\s*(\d+),\s*(\d+)\)', rect_str)
                                if rect_match:
                                    x1, y1, x2, y2 = map(int, rect_match.groups())
                                    center_x = (x1 + x2) // 2
                                    center_y = (y1 + y2) // 2
                                    self.log(f"通过{element_name}控件rect字符串解析获取坐标: ({center_x}, {center_y})", "debug")
                            except Exception as rect_e:
                                self.log(f"获取{element_name}控件rect失败: {rect_e}", "debug")

                    if center_x is not None and center_y is not None:
                        self.log(f"使用HID设备点击{element_name}控件坐标: ({center_x}, {center_y})", "info")
                        self.hid_device.click(center_x, center_y, dur=50)
                        self.log(f"HID点击{element_name}控件成功", "success")
                        click_success = True
                    else:
                        self.log(f"无法获取{element_name}控件坐标，尝试其他方式", "warning")

                except Exception as hid_e:
                    self.log(f"HID点击{element_name}控件失败: {hid_e}", "warning")

            # 方法2: 如果HID点击失败，尝试使用AScript的click函数
            if not click_success:
                try:
                    center_x, center_y = None, None

                    # 尝试多种方式获取坐标（与HID方式相同）
                    if hasattr(element, 'bounds'):
                        try:
                            bounds = element.bounds
                            if callable(bounds):
                                bounds = bounds()

                            if bounds and hasattr(bounds, 'left'):
                                center_x = (bounds.left + bounds.right) // 2
                                center_y = (bounds.top + bounds.bottom) // 2
                        except:
                            pass

                    # 尝试通过字符串解析获取坐标信息
                    if center_x is None:
                        try:
                            element_str = str(element)
                            self.log(f"{element_name}控件信息: {element_str}", "debug")

                            # 尝试从字符串中解析坐标信息
                            import re
                            bounds_match = re.search(r'"bounds":\s*\[(\d+),\s*(\d+),\s*(\d+),\s*(\d+)\]', element_str)
                            if bounds_match:
                                x1, y1, x2, y2 = map(int, bounds_match.groups())
                                center_x = (x1 + x2) // 2
                                center_y = (y1 + y2) // 2
                                self.log(f"通过{element_name}控件字符串解析获取坐标: ({center_x}, {center_y})", "debug")
                        except Exception as parse_e:
                            self.log(f"{element_name}控件字符串解析失败: {parse_e}", "debug")

                    if center_x is not None and center_y is not None:
                        self.log(f"使用AScript click函数点击{element_name}控件坐标: ({center_x}, {center_y})", "info")
                        click(center_x, center_y)
                        self.log(f"AScript点击{element_name}控件成功", "success")
                        click_success = True
                    else:
                        self.log(f"无法获取{element_name}控件坐标，尝试元素点击方法", "warning")

                except Exception as click_e:
                    self.log(f"AScript点击{element_name}控件失败: {click_e}", "warning")

            # 方法3: 最后尝试元素的点击方法（如果支持）
            if not click_success:
                try:
                    if hasattr(element, 'click'):
                        element.click()
                        self.log(f"元素点击{element_name}控件成功", "success")
                        click_success = True
                    else:
                        self.log(f"{element_name}控件不支持click方法", "warning")
                except Exception as elem_e:
                    self.log(f"元素点击{element_name}控件失败: {elem_e}", "warning")

            return click_success

        except Exception as e:
            self.log(f"点击{element_name}控件异常: {e}", "error")
            return False

    def click_element_with_hid_only(self, element, element_name):
        """仅使用HID设备点击控件的方法"""
        try:
            if not self.hid_device:
                self.log(f"没有HID设备，无法点击{element_name}控件", "error")
                return False

            center_x, center_y = None, None

            # 优先尝试直接从控件字典中获取center_x和center_y
            if isinstance(element, dict):
                if 'center_x' in element and 'center_y' in element:
                    center_x = element['center_x']
                    center_y = element['center_y']
                    self.log(f"从{element_name}控件字典获取坐标: ({center_x}, {center_y})", "debug")
            elif hasattr(element, 'center_x') and hasattr(element, 'center_y'):
                center_x = element.center_x
                center_y = element.center_y
                self.log(f"从{element_name}控件属性获取坐标: ({center_x}, {center_y})", "debug")

            # 如果没有直接的center坐标，尝试其他方式
            if center_x is None:
                # 尝试bounds属性
                if hasattr(element, 'bounds'):
                    try:
                        bounds = element.bounds
                        if callable(bounds):
                            bounds = bounds()

                        if bounds and hasattr(bounds, 'left'):
                            center_x = (bounds.left + bounds.right) // 2
                            center_y = (bounds.top + bounds.bottom) // 2
                            self.log(f"通过{element_name}控件bounds属性获取坐标: ({center_x}, {center_y})", "debug")
                    except Exception as bounds_e:
                        self.log(f"获取{element_name}控件bounds失败: {bounds_e}", "debug")

                # 尝试从rect字符串解析坐标
                if center_x is None and hasattr(element, 'rect'):
                    try:
                        rect = element.rect
                        rect_str = str(rect)
                        self.log(f"{element_name}控件rect字符串: {rect_str}", "debug")

                        # 解析类似 "Rect(489, 2061 - 653, 2116)" 的字符串
                        import re
                        rect_match = re.search(r'Rect\((\d+),\s*(\d+)\s*-\s*(\d+),\s*(\d+)\)', rect_str)
                        if rect_match:
                            x1, y1, x2, y2 = map(int, rect_match.groups())
                            center_x = (x1 + x2) // 2
                            center_y = (y1 + y2) // 2
                            self.log(f"通过{element_name}控件rect字符串解析获取坐标: ({center_x}, {center_y})", "debug")
                    except Exception as rect_e:
                        self.log(f"获取{element_name}控件rect失败: {rect_e}", "debug")

            if center_x is not None and center_y is not None:
                self.log(f"使用HID设备点击{element_name}控件坐标: ({center_x}, {center_y})", "info")
                self.hid_device.click(center_x, center_y, dur=50)
                self.log(f"HID点击{element_name}控件成功", "success")
                return True
            else:
                self.log(f"无法获取{element_name}控件坐标", "error")
                return False

        except Exception as e:
            self.log(f"HID点击{element_name}控件异常: {e}", "error")
            return False

    def start_second_round(self):
        """开始第二轮操作循环"""
        try:
            self.log("🔄 开始第二轮操作循环", "info")
            self.update_status("第二轮循环：查找赚钱图标...")

            # 等待界面稳定
            time.sleep(3)

            # 重新开始查找赚钱图标
            try:
                self.click_money_icon()
                self.log("第二轮：成功执行赚钱图标点击流程", "success")
            except Exception as e:
                self.log(f"第二轮：赚钱图标点击流程失败: {e}", "warning")
                # 显示完成提示
                self.show_completion_message()
                # 设置全局完成标志
                self.task_completed = True

        except Exception as e:
            self.log(f"第二轮操作循环失败: {e}", "error")
            # 显示完成提示
            self.show_completion_message()
            # 设置全局完成标志
            self.task_completed = True

    def show_completion_message(self):
        """显示任务完成消息"""
        try:
            self.log("🎉🎉🎉 任务完成提示 🎉🎉🎉", "success")
            self.log("✅ 所有视频滑动任务已完成", "success")
            self.log("✅ 已成功点击'回任务页'按钮", "success")
            self.log("✅ 程序执行完毕", "success")

            # 如果UI可用，显示完成对话框
            if self.window and self.page_loaded:
                try:
                    # 调用前端显示完成消息
                    completion_message = {
                        "title": "任务完成",
                        "message": "🎉 恭喜！所有视频滑动任务已完成！\\n\\n✅ 已成功点击'回任务页'按钮\\n✅ 程序执行完毕",
                        "type": "success"
                    }
                    self.window.call(f"if(typeof showCompletionMessage === 'function') showCompletionMessage({json.dumps(completion_message)})")
                except Exception as ui_e:
                    self.log(f"显示UI完成消息失败: {ui_e}", "warning")

        except Exception as e:
            self.log(f"显示完成消息失败: {e}", "error")

    def monitor_coin_increase(self):
        """监控金币增加状态，支持并发的倒计时和转圈任务"""
        try:
            # 检查任务是否已完成
            if self.task_completed:
                self.log("任务已完成，停止监控", "info")
                return

            self.log("开始监控金币增加状态...", "info")
            self.update_status("监控金币状态...")

            # 等待金币元素加载
            time.sleep(2)

            # 最大监控时间(秒)
            max_monitor_time = 120  # 最多监控2分钟
            start_time = time.time()

            # 记录上一次的金币数值
            last_coin_value = None
            same_value_count = 0

            # 任务状态跟踪
            countdown_task_completed = False  # 倒计时任务是否完成
            spinning_task_completed = False   # 转圈任务是否完成
            has_countdown_task = False        # 是否存在倒计时任务

            # 每5秒检查一次金币数值和任务状态
            check_interval = 5
            last_check_time = 0

            # 每10秒检查一次"回任务页"控件（降低检测频率）
            back_to_task_check_interval = 10
            last_back_to_task_check_time = 0

            while time.time() - start_time < max_monitor_time:
                current_time = time.time()

                # 检查任务是否已完成
                if self.task_completed:
                    self.log("检测到任务完成标志，停止监控", "info")
                    return

                # 每10秒检查一次是否有"回任务页"控件
                if current_time - last_back_to_task_check_time >= back_to_task_check_interval:
                    last_back_to_task_check_time = current_time
                    if self.check_and_click_back_to_task():
                        self.log("在监控过程中发现'回任务页'控件，任务完成", "success")
                        return  # 直接返回，不再继续监控

                # 每5秒检查一次任务状态和金币数值
                if current_time - last_check_time >= check_interval:
                    last_check_time = current_time

                    # 检查是否有"看本视频"倒计时提示
                    watch_video_tip = Selector().text("看本视频").type("LynxFlattenUI").find()
                    if watch_video_tip:
                        has_countdown_task = True
                        if not countdown_task_completed:
                            self.log("发现'看本视频'倒计时任务，正在等待中...", "info")
                            self.update_status("等待倒计时任务...")

                    # 检查倒计时任务是否完成 - "已发放"提示
                    reward_done_tip = Selector().text("已发放").desc("已发放").type("LynxFlattenUI").find()
                    if reward_done_tip and has_countdown_task and not countdown_task_completed:
                        self.log("🎉 倒计时任务完成！发现'已发放'提示", "success")
                        countdown_task_completed = True
                        self.update_status("倒计时任务已完成")

                    self.log(f"第{same_value_count+1}次检查任务状态...", "info")

                    # 获取金币数值来判断转圈任务状态
                    coin_element = Selector().id("c2l").type("ImageView").packageName("com.ss.android.ugc.aweme.lite").brother(3).find()

                    if coin_element:
                        # 尝试获取文本值
                        coin_value = None
                        try:
                            if hasattr(coin_element, 'text') and coin_element.text():
                                coin_value = coin_element.text()
                            elif isinstance(coin_element, dict) and 'text' in coin_element:
                                coin_value = coin_element['text']
                        except:
                            pass

                        # 如果无法直接获取文本，尝试获取元素的完整信息并提取
                        if not coin_value:
                            try:
                                element_info = str(coin_element)
                                import re
                                text_match = re.search(r"'text':\s*'([^']*)'", element_info)
                                if text_match:
                                    coin_value = text_match.group(1)
                            except:
                                pass

                        # 记录并比较金币值来判断转圈任务状态
                        if coin_value:
                            self.log(f"当前金币数值: {coin_value}", "info")

                            if coin_value == last_coin_value:
                                same_value_count += 1
                                self.log(f"金币数值保持不变 ({same_value_count}/3)", "info")

                                # 如果连续3次检查（15秒）金币数值相同，认为转圈任务可能完成
                                if same_value_count >= 3:
                                    if not spinning_task_completed:
                                        self.log("🎉 转圈任务完成！金币数值连续3次保持不变", "success")
                                        spinning_task_completed = True
                                        self.update_status("转圈任务已完成")
                            else:
                                same_value_count = 0
                                self.log("金币数值发生变化，转圈任务继续进行", "info")
                                spinning_task_completed = False  # 重置转圈任务状态

                            last_coin_value = coin_value
                        else:
                            self.log("无法获取金币数值", "warning")
                    else:
                        self.log("未找到金币元素", "warning")

                    # 检查所有任务是否完成（只在5秒检查周期内执行）
                    all_tasks_completed = self.check_all_tasks_completed(
                        has_countdown_task, countdown_task_completed, spinning_task_completed
                    )

                    if all_tasks_completed:
                        self.log("🎊 所有任务已完成，准备滑到下一个视频", "success")
                        break

                # 稍微延迟一下，减少CPU使用
                time.sleep(0.5)

            # 检查任务是否已完成，如果完成则不再继续
            if self.task_completed:
                self.log("任务已完成，停止所有操作", "success")
                return

            # 监控结束，滑到下一个视频
            self.log("金币监控结束，准备滑到下一个视频", "info")

            # 使用单独的方法处理滑动，提高成功率
            if self.slide_to_next_video():
                # 检查滑动过程中是否检测到任务完成
                if self.task_completed:
                    self.log("滑动过程中检测到任务完成，停止操作", "success")
                    return

                # 等待新视频加载
                self.log("等待新视频加载...", "info")
                time.sleep(3)

                # 再次检查任务是否完成
                if self.task_completed:
                    self.log("任务已完成，不再监控新视频", "success")
                    return

                # 重新开始监控新视频的金币增加状态
                self.log("开始监控新视频的金币状态...", "info")
                self.monitor_coin_increase()

        except Exception as e:
            self.log(f"监控金币增加状态失败: {e}", "error")
            self.update_status("监控失败")

    def check_all_tasks_completed(self, has_countdown_task, countdown_task_completed, spinning_task_completed):
        """检查所有任务是否完成"""
        try:
            # 如果有倒计时任务，需要两个任务都完成
            if has_countdown_task:
                if countdown_task_completed and spinning_task_completed:
                    self.log("✅ 倒计时任务和转圈任务都已完成", "success")
                    self.update_status("所有任务已完成")
                    return True
                else:
                    # 只在状态发生变化时显示任务状态，避免重复日志
                    countdown_status = "✅ 已完成" if countdown_task_completed else "⏳ 进行中"
                    spinning_status = "✅ 已完成" if spinning_task_completed else "⏳ 进行中"

                    # 检查状态是否发生变化
                    current_status = f"倒计时: {countdown_status}, 转圈: {spinning_status}"
                    if not hasattr(self, '_last_task_status') or self._last_task_status != current_status:
                        self.log(f"任务状态 - {current_status}", "info")
                        self._last_task_status = current_status
                    return False
            else:
                # 只有转圈任务，只需要转圈任务完成
                if spinning_task_completed:
                    self.log("✅ 转圈任务已完成（无倒计时任务）", "success")
                    self.update_status("转圈任务已完成")
                    return True
                else:
                    # 只在首次或状态变化时显示，避免重复日志
                    if not hasattr(self, '_last_spinning_status') or self._last_spinning_status != "进行中":
                        self.log("⏳ 转圈任务进行中（无倒计时任务）", "info")
                        self._last_spinning_status = "进行中"
                    return False

        except Exception as e:
            self.log(f"检查任务状态失败: {e}", "error")
            return False

    def check_money_icon_available(self):
        """检查赚钱图标是否可用"""
        try:
            self.log("检查赚钱图标是否可用...", "info")

            # 使用FindImages查找赚钱图标
            results = FindImages.find_all_template([R.img("赚钱.png")], confidence=0.95)

            if results and len(results) > 0:
                self.log("找到赚钱图标，返回操作成功", "success")
                return True
            else:
                self.log("未找到赚钱图标，返回操作可能失败", "warning")
                return False

        except Exception as e:
            self.log(f"检查赚钱图标失败: {e}", "error")
            return False

    def get_device_info_ui(self):
        """获取设备信息并更新UI"""
        try:
            width, height = get_device_info()
            self.screen_width = width
            self.screen_height = height

            device_info = {
                "width": width,
                "height": height,
                "ratio": f"{width}:{height}"
            }

            if self.window:
                try:
                    self.window.call(f"if(typeof updateDeviceInfo === 'function') updateDeviceInfo({json.dumps(device_info)})")
                except Exception as e:
                    if DEBUG_MODE:
                        print(f"更新设备信息失败: {e}")

            self.log(f"设备信息: {width}x{height}")

        except Exception as e:
            self.log(f"获取设备信息失败: {e}", "error")

    def save_config(self, config_data):
        """保存配置"""
        try:
            # 使用统一的数据保存接口
            if save_data("douyin_config", config_data):
                self.log("配置保存成功", "success")
            else:
                self.log("配置保存失败", "error")
        except Exception as e:
            self.log(f"配置保存失败: {e}", "error")

    def load_config(self):
        """加载配置"""
        try:
            config_data = load_data("douyin_config", {})

            # 确保config_data是字典类型
            if not isinstance(config_data, dict):
                config_data = {}

            if self.window:
                try:
                    self.window.call(f"if(typeof loadConfigData === 'function') loadConfigData({json.dumps(config_data)})")
                except Exception as e:
                    self.log(f"UI配置更新失败: {e}", "warning")

            self.log("配置加载成功")
            return config_data
        except Exception as e:
            self.log(f"配置加载失败: {e}", "error")
            return {}

    def clear_logs(self):
        """清空日志"""
        self.log_messages = []
        if self.window:
            try:
                self.window.call("if(typeof clearLogs === 'function') clearLogs()")
            except Exception as e:
                if DEBUG_MODE:
                    print(f"清空日志失败: {e}")
        self.log("日志已清空")

    def minimize_to_float(self):
        """最小化为侧边栏悬浮窗"""
        if self.window and not self.is_minimized:
            try:
                # 设置为侧边栏尺寸（更小更精致）
                sidebar_width = 80   # 侧边栏宽度
                sidebar_height = 120  # 侧边栏高度
                self.window.size(sidebar_width, sidebar_height)

                # 设置为悬浮模式
                self.window.drag(True)
                self.window.dim_amount(0)  # 移除背景遮罩

                # 设置窗口位置到屏幕右侧边栏
                try:
                    # 计算右侧边栏位置
                    x_position = self.screen_width - sidebar_width - 20  # 距离右边缘20px
                    y_position = int(self.screen_height * 0.3)  # 屏幕30%高度位置

                    # 设置窗口位置（如果AScript支持position方法）
                    if hasattr(self.window, 'position'):
                        self.window.position(x_position, y_position)
                    elif hasattr(self.window, 'move'):
                        self.window.move(x_position, y_position)
                except Exception as pos_e:
                    if DEBUG_MODE:
                        print(f"设置窗口位置失败: {pos_e}")

                # 通知前端切换到最小化界面
                if self.page_loaded:
                    try:
                        self.window.call("if(typeof switchToMinimizedView === 'function') switchToMinimizedView()")
                    except Exception as call_e:
                        if DEBUG_MODE:
                            print(f"调用最小化界面失败: {call_e}")

                self.is_minimized = True
                self.log("界面已最小化为侧边栏", "info")
            except Exception as e:
                self.log(f"最小化失败: {e}", "error")

    def restore_from_float(self):
        """从悬浮窗恢复"""
        if self.window and self.is_minimized:
            try:
                # 恢复正常尺寸（使用像素值）
                window_width = int(self.screen_width * 0.95)  # 屏幕95%宽度
                window_height = int(self.screen_height * 0.90)  # 屏幕90%高度

                # 确保窗口尺寸不会太小
                window_width = max(window_width, 800)
                window_height = max(window_height, 600)

                self.window.size(window_width, window_height)
                # 恢复背景遮罩
                self.window.dim_amount(0.3)

                # 通知前端切换到正常界面
                if self.page_loaded:
                    try:
                        self.window.call("if(typeof switchToNormalView === 'function') switchToNormalView()")
                    except Exception as call_e:
                        if DEBUG_MODE:
                            print(f"调用正常界面失败: {call_e}")

                self.is_minimized = False
                self.log("界面已恢复正常", "info")
            except Exception as e:
                self.log(f"恢复界面失败: {e}", "error")

    def hide_to_float(self):
        """隐藏界面并显示悬浮窗"""
        try:
            # 显示悬浮窗
            if UI_AVAILABLE:
                try:
                    # 显示悬浮窗在屏幕右侧
                    FloatWindow.show(0.9, 0.3, 0.9)  # x=90%屏幕宽度, y=30%屏幕高度, 透明度=0.9
                    self.log("悬浮窗已显示，点击悬浮窗可恢复界面", "success")

                    # 添加恢复界面按钮到悬浮窗菜单
                    def restore_ui():
                        """恢复界面回调函数"""
                        try:
                            FloatWindow.hide()  # 隐藏悬浮窗
                            # 延迟一点时间再显示主界面，确保悬浮窗完全隐藏
                            time.sleep(0.5)
                            self.show_ui()  # 重新显示主界面
                            self.log("界面已从悬浮窗恢复", "info")
                        except Exception as e:
                            self.log(f"从悬浮窗恢复界面失败: {e}", "error")

                    # 尝试添加恢复按钮到悬浮窗菜单
                    try:
                        # 检查是否有图标文件
                        icon_path = None
                        if hasattr(R, 'res'):
                            try:
                                icon_path = R.res("icon/restore.png")
                                # 检查文件是否真的存在
                                import os
                                if not os.path.exists(icon_path):
                                    icon_path = None
                            except:
                                icon_path = None

                        if icon_path:
                            FloatWindow.add_menu("restore_ui", icon_path, restore_ui, False)
                            self.log("悬浮窗恢复按钮已添加", "info")
                        else:
                            # 没有图标文件时，仍然尝试添加菜单（可能使用默认图标）
                            try:
                                # 使用空字符串或默认路径
                                FloatWindow.add_menu("restore_ui", "", restore_ui, False)
                                self.log("悬浮窗恢复按钮已添加（无自定义图标）", "info")
                            except:
                                self.log("无法添加悬浮窗菜单按钮，请手动点击悬浮窗恢复", "warning")

                    except Exception as menu_e:
                        self.log(f"添加悬浮窗菜单失败: {menu_e}", "warning")
                        self.log("您可以通过点击悬浮窗来恢复界面", "info")

                except Exception as e:
                    self.log(f"显示悬浮窗失败: {e}", "error")
                    # 悬浮窗失败时，使用原来的最小化方式作为备用
                    self.log("尝试使用最小化方式作为备用", "info")
                    self.minimize_to_float()
                    return
            else:
                self.log("UI功能不可用，无法显示悬浮窗", "error")
                return

            # 悬浮窗显示成功后，隐藏主窗口
            if self.window:
                # 确保在关闭窗口前设置好所有必要的状态
                self.log("准备隐藏主界面", "info")
                # 短暂延迟以确保悬浮窗完全显示
                time.sleep(0.3)
                self.window.close()
                self.window = None
                self.log("主界面已隐藏", "info")

        except Exception as e:
            self.log(f"隐藏到悬浮窗失败: {e}", "error")

    def close_window(self):
        """关闭窗口"""
        if self.window:
            self.window.close()
            self.window = None
        self.log("窗口已关闭")

    def show_ui(self, use_simple=False):
        """显示UI界面"""
        if not UI_AVAILABLE:
            self.log("UI功能不可用，WebWindow模块未找到", "error")
            print("❌ UI功能不可用，请检查AScript版本是否支持WebWindow")
            return False

        try:
            # 先获取屏幕尺寸
            try:
                self.screen_width, self.screen_height = get_device_info()
                if DEBUG_MODE:
                    print(f"获取到屏幕尺寸: {self.screen_width} x {self.screen_height}")
            except Exception as e:
                if DEBUG_MODE:
                    print(f"获取屏幕尺寸失败，使用默认值: {e}")
                # 使用默认尺寸
                self.screen_width, self.screen_height = 1080, 2340

            # 选择HTML文件
            if use_simple:
                html_path = R.res("ui/simple.html")
                self.log("使用简化版UI界面")
            else:
                html_path = R.res("ui/main.html")
                self.log("使用完整版UI界面")

            # 创建WebWindow
            self.window = WebWindow(html_path, self.tunnel_handler)

            # 设置窗口属性（使用像素值）
            # 根据屏幕分辨率计算合适的窗口尺寸
            window_width = int(self.screen_width * 0.95)  # 屏幕95%宽度
            window_height = int(self.screen_height * 0.90)  # 屏幕90%高度

            # 确保窗口尺寸不会太小
            window_width = max(window_width, 800)  # 最小宽度800px
            window_height = max(window_height, 600)  # 最小高度600px

            if DEBUG_MODE:
                print(f"设置窗口尺寸: {window_width} x {window_height}")

            self.window.size(window_width, window_height)
            self.window.background("#FFFFFF")
            self.window.drag(True)  # 允许拖拽
            self.window.dim_amount(0.3)  # 半透明遮罩

            # 显示窗口
            self.window.show()

            self.log("UI界面已启动", "success")

            # 等待页面加载完成
            max_wait = 10  # 最多等待10秒
            wait_count = 0
            while not self.page_loaded and wait_count < max_wait:
                time.sleep(1)
                wait_count += 1

            if self.page_loaded:
                self.log("页面加载完成，开始初始化数据", "info")

                try:
                    self.load_config()
                except Exception as e:
                    self.log(f"配置加载异常: {e}", "warning")

                try:
                    self.get_device_info_ui()
                except Exception as e:
                    self.log(f"设备信息获取异常: {e}", "warning")

                # 自动连接HID设备
                try:
                    self.auto_connect_hid()
                except Exception as e:
                    self.log(f"HID设备自动连接异常: {e}", "warning")
            else:
                self.log("页面加载超时，跳过数据初始化", "warning")

            return True

        except Exception as e:
            self.log(f"UI界面启动失败: {e}", "error")
            print(f"UI启动失败: {e}")

            # 如果完整版失败，尝试简化版
            if not use_simple:
                self.log("尝试启动简化版UI界面", "warning")
                return self.show_ui(use_simple=True)
            return False

# 全局UI控制器实例
ui_controller = DouyinUIController()

# ==================== 启动函数 ====================

def show_ui():
    """显示UI界面"""
    if not UI_AVAILABLE:
        print("❌ UI功能不可用，WebWindow模块未找到")
        print("请检查AScript版本是否支持WebWindow组件")
        return False
    return ui_controller.show_ui()

def show_startup_menu():
    """显示启动菜单"""
    print("🎬 抖音自动化脚本 - AScript版")
    print("=" * 50)
    print("可用运行模式:")

    if UI_AVAILABLE:
        print("1. 图形界面模式 (推荐)")
        print("2. 仅启动抖音应用")
        print("3. 仅测试HID连接")
        print("4. 传统模式演示")
    else:
        print("1. 仅启动抖音应用 (推荐)")
        print("2. 仅测试HID连接")
        print("3. 传统模式演示")
        print("   注意: UI功能不可用")

    print("=" * 50)

    try:
        # 在AScript环境中，input()不可用，所以自动选择默认模式
        print("⚠️  检测到AScript环境，自动选择推荐模式...")

        if UI_AVAILABLE:
            print("🖥️  启动图形界面模式...")
            if not show_ui():
                print("UI启动失败")
        else:
            print("📱 启动抖音应用...")
            if start_douyin():
                print("✅ 抖音启动成功!")
            else:
                print("❌ 抖音启动失败!")

    except Exception as e:
        print(f"❌ 启动异常: {e}")
        print("启动失败")

def manual_mode_selection(mode):
    """手动模式选择（用于外部调用）"""
    print(f"🎯 手动选择模式: {mode}")

    if mode == "ui" or mode == "1":
        if UI_AVAILABLE:
            print("🖥️  启动图形界面...")
            if not show_ui():
                print("UI启动失败，切换到命令行模式...")
                main()
        else:
            print("❌ UI功能不可用，切换到命令行模式...")
            main()
    elif mode == "auto" or mode == "2":
        print("🚀 启动命令行自动化...")
        main()
    elif mode == "launch" or mode == "3":
        print("📱 启动抖音应用...")
        if start_douyin():
            print("✅ 抖音启动成功!")
        else:
            print("❌ 抖音启动失败!")
    elif mode == "test" or mode == "4":
        print("🔌 测试HID连接...")
        test_hid_connection()
    elif mode == "demo" or mode == "5":
        print("🖱️  传统模式演示...")
        width, height = get_device_info()
        fallback_operations(width, height)
    else:
        print("❌ 未知模式，启动默认模式...")
        show_startup_menu()

# ==================== 主入口 ====================

# AScript框架会自动执行这个文件
print("🚀 抖音自动化脚本正在启动...")
print(f"📋 UI功能: {'可用' if UI_AVAILABLE else '不可用'}")
print(f"💾 存储功能: {'kv存储' if KV_AVAILABLE else '文件存储'}")

if 'DEBUG_MODE' in globals() and DEBUG_MODE:
    print("🐛 调试模式已启用")

# 手动模式选择（可以修改这里来选择不同的启动模式）
# 可选模式: "ui", "launch", "test", "demo"
# 留空或注释掉则显示菜单并自动选择推荐模式

MANUAL_MODE = None  # 设置为 "ui", "launch", "test", "demo" 之一来直接启动对应模式

if MANUAL_MODE:
    manual_mode_selection(MANUAL_MODE)
else:
    # 显示启动菜单并自动选择推荐模式
    show_startup_menu()

# 传统点击方式示例（备用）
# 如果需要手动测试传统点击，可以取消下面的注释
# click(100,100)
