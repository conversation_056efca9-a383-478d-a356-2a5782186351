#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
图片模板匹配测试脚本
用于测试直播中.png图片的识别效果
"""

import os
import sys

def test_image_template():
    """测试图片模板匹配功能"""
    print("🔍 图片模板匹配测试")
    print("=" * 50)
    
    # 检查图片文件是否存在
    img_path = "doyin/res/img/直播中.png"
    placeholder_path = "doyin/res/img/直播中.png.placeholder"
    
    print(f"📁 检查图片文件: {img_path}")
    
    if os.path.exists(img_path):
        print("✅ 找到直播中.png图片文件")
        file_size = os.path.getsize(img_path)
        print(f"📏 文件大小: {file_size} 字节")
        
        if file_size < 1000:
            print("⚠️  警告: 文件大小较小，可能不是有效的PNG图片")
        else:
            print("✅ 文件大小正常")
            
    else:
        print("❌ 未找到直播中.png图片文件")
        
        if os.path.exists(placeholder_path):
            print("📝 发现占位符文件，请按以下步骤操作：")
            print("   1. 在抖音应用中截取'直播中'相关图片")
            print("   2. 保存为PNG格式")
            print("   3. 命名为'直播中.png'")
            print("   4. 放置在doyin/res/img/目录下")
            print("   5. 删除占位符文件")
        else:
            print("📝 请创建直播中.png图片文件")
    
    print("\n" + "=" * 50)
    
    # 如果在AScript环境中，尝试测试图片识别
    try:
        from ascript.android.screen import FindImages
        from ascript.android.system import R
        
        print("🔧 AScript环境检测成功")
        
        if os.path.exists(img_path):
            print("🔍 尝试图片模板匹配...")
            try:
                # 测试图片识别
                results = FindImages.find_all_template([R.img("直播中.png")], confidence=0.95)
                
                if results and len(results) > 0:
                    print(f"✅ 图片识别成功！找到 {len(results)} 个匹配项")
                    for i, result in enumerate(results):
                        print(f"   匹配项 {i+1}: {result}")
                else:
                    print("❌ 未找到匹配的图片")
                    print("💡 建议：")
                    print("   - 检查图片质量是否清晰")
                    print("   - 确保图片内容与当前界面匹配")
                    print("   - 可以尝试降低confidence值（当前0.95）")
                    
            except Exception as e:
                print(f"❌ 图片识别测试失败: {e}")
        else:
            print("⏭️  跳过图片识别测试（图片文件不存在）")
            
    except ImportError:
        print("⚠️  非AScript环境，跳过图片识别测试")
    except Exception as e:
        print(f"❌ AScript环境检测失败: {e}")
    
    print("\n📋 测试完成")

def show_image_requirements():
    """显示图片要求说明"""
    print("\n📸 图片要求说明")
    print("=" * 50)
    print("🎯 直播中.png 图片要求：")
    print("   ✓ 格式: PNG")
    print("   ✓ 尺寸: 建议不超过200x200像素")
    print("   ✓ 内容: 包含'直播中'相关的视觉元素")
    print("   ✓ 背景: 应与实际应用中的背景相匹配")
    print("   ✓ 质量: 清晰度要足够，避免模糊")
    print("\n🔧 识别参数：")
    print("   ✓ 置信度: 0.95 (95%匹配度)")
    print("   ✓ 查找方式: find_all_template")
    print("   ✓ 点击策略: 点击找到的第一个匹配项")
    print("\n💡 优化建议：")
    print("   ✓ 在不同光线条件下测试识别效果")
    print("   ✓ 如果识别率不高，可以调整confidence参数")
    print("   ✓ 可以准备多个不同状态下的图片")

if __name__ == "__main__":
    print("🎬 抖音自动化脚本 - 图片模板匹配测试")
    print("版本: 1.0")
    print("更新时间: 2024-12-19")
    print()
    
    # 运行测试
    test_image_template()
    
    # 显示图片要求
    show_image_requirements()
    
    print("\n" + "=" * 50)
    print("📚 更多信息请参考:")
    print("   - doyin/res/img/README.md")
    print("   - IMAGE_TEMPLATE_UPDATE.md")
    print("=" * 50)
