# 抖音自动化脚本

这是一个基于AScript框架的抖音自动化脚本，支持使用ESP32 HID设备进行更稳定的操作。

## 功能特性

- ✅ **图形界面控制** - 基于AScript WebWindow和layui移动端组件
- ✅ **图片模板匹配** - 使用图像识别技术提高控件识别准确性
- ✅ **智能重试机制** - 返回按钮支持多层备用方案和重试验证
- ✅ **优化观看时长** - 直播观看时间调整为10-15分钟，提高效率
- ✅ 自动启动抖音轻量版应用 (com.ss.android.ugc.aweme.lite)
- ✅ 支持ESP32蓝牙HID设备操作
- ✅ 支持ESP32 USB HID设备操作
- ✅ 传统点击方式备用方案
- ✅ 自动获取设备屏幕分辨率
- ✅ 丰富的抖音操作功能
- ✅ 实时日志监控和状态显示
- ✅ 可视化配置管理

## 支持的操作

### 基础操作
- 点击播放/暂停
- 向上滑动切换视频
- 向下滑动返回上一视频
- 点击点赞按钮
- 点击评论按钮
- 返回操作

### 高级操作
- 长按视频（弹出菜单）
- 双击点赞
- 左右滑动切换页面
- 精确的滑动控制

## 项目结构

```
code/doyin/
├── doyin/                     # AScript项目主目录
│   ├── __init__.py           # ⭐ AScript启动文件（主入口）
│   ├── build.as              # 依赖配置文件
│   └── res/                  # 资源文件目录
│       ├── img/              # 图片资源目录
│       │   ├── README.md     # 图片资源说明
│       │   ├── 直播中.png     # 直播识别图片（需用户提供）
│       │   ├── 返回.png       # 返回按钮图片（需用户提供）
│       │   └── 赚钱.png       # 赚钱图标图片（需用户提供）
│       └── ui/               # UI界面文件
│           ├── main.html     # 完整版UI界面
│           └── simple.html   # 简化版UI界面
├── config.py                 # 配置文件
├── README.md                 # 项目说明文档
├── IMAGE_TEMPLATE_UPDATE.md  # 图片模板匹配更新记录
├── RETURN_BUTTON_UPDATE.md   # 返回按钮图片匹配更新记录
├── WATCH_TIME_UPDATE.md      # 观看时间调整更新记录
└── JAVASCRIPT_ERROR_FIX.md   # 最新修复记录
```

## 环境要求

### 软件要求
- AScript Android自动化框架
- Python环境
- 已安装的依赖包（见build.as文件）

### 硬件要求（可选）
- ESP32开发板（支持的型号）
  - esp32-c3 (蓝牙)
  - esp32-pico (蓝牙)
  - esp32-s3 (蓝牙+USB)

## 使用方法

### 📋 准备工作

#### 1. 图片资源准备
在使用脚本前，需要准备必要的图片资源：

1. **直播中.png** - 用于识别直播控件
   - 在抖音应用中找到"直播中"相关的视觉元素
   - 截取包含该元素的图片并保存为PNG格式
   - 将文件命名为 `直播中.png` 并放置在 `doyin/res/img/` 目录下

2. **返回.png** - 用于识别返回按钮
   - 在抖音应用中找到返回按钮
   - 截取包含返回按钮的图片并保存为PNG格式
   - 将文件命名为 `返回.png` 并放置在 `doyin/res/img/` 目录下

3. **赚钱.png** - 用于识别赚钱图标
   - 在抖音应用中找到赚钱相关的图标
   - 截取包含该图标的图片并保存为PNG格式
   - 将文件命名为 `赚钱.png` 并放置在 `doyin/res/img/` 目录下

4. **详细要求请参考** `doyin/res/img/README.md`

5. **删除占位符文件**
   - 删除所有 `.placeholder` 后缀的占位符文件

### 🎯 AScript框架中运行（推荐）

**这是标准的AScript项目，建议在AScript框架中运行：**

1. **导入项目到AScript**
   - 将 `doyin` 文件夹复制到AScript项目目录
   - 或在AScript中创建新项目并复制文件

2. **自动启动**
   - AScript会自动执行 `doyin/__init__.py`
   - 启动后显示选择菜单：
     ```
     🎬 抖音自动化脚本 - AScript版
     请选择运行模式:
     1. 图形界面模式 (推荐)
     2. 命令行自动化模式
     3. 仅启动抖音应用
     4. 仅测试HID连接
     5. 传统模式演示
     0. 退出
     ```

### 🖥️ 自定义运行方式

如果需要自定义运行特定功能，可以修改 `doyin/__init__.py` 文件中的启动逻辑，或者在启动菜单中选择对应的模式。

## HID设备设置

### ESP32固件刷入
1. 下载固件：[ESP32固件包](https://airscript.oss-cn-hangzhou.aliyuncs.com/hid/AS-esp32%E5%9B%BA%E4%BB%B6%E7%83%A7%E5%BD%95%E5%8C%85v4.1.2.zip)
2. 安装Python和esptool：`pip install esptool`
3. 连接ESP32到电脑
4. 进入烧录模式：按住boot键，点击reset键，松开boot键
5. 运行烧录工具.bat

### 蓝牙HID设置
1. 刷入蓝牙固件
2. 在Android设备上打开蓝牙
3. 搜索并配对AS开头的设备
4. 运行脚本

### USB HID设置
1. 刷入USB固件
2. 用USB线连接ESP32和Android设备
3. 在AScript中授权USB设备
4. 运行脚本

## 配置说明

### 屏幕坐标说明
脚本会自动获取设备屏幕分辨率，主要操作区域：
- 屏幕中央：播放/暂停
- 右侧90%位置：点赞、评论按钮
- 上下滑动：切换视频

### 滑动参数
- **蓝牙设备**：支持缓动模式、缓动力度等高级参数
- **USB设备**：支持基本的滑动时长控制

## 🖥️ 图形界面功能

### 界面特性
- **移动端优化** - 基于layui移动端组件，适配手机屏幕
- **实时监控** - 显示运行状态、HID设备连接状态
- **可视化控制** - 一键启动、停止、测试各种功能
- **配置管理** - 图形化配置参数，支持保存和加载
- **日志查看** - 实时显示运行日志，支持不同级别的日志

### 界面布局
1. **控制面板** - 主要操作按钮和设备状态
2. **设备信息** - 显示屏幕分辨率、应用信息等
3. **配置设置** - 可视化配置各种参数
4. **运行日志** - 实时日志显示和管理

### UI操作说明
- **停止运行** - 中断当前运行的操作
- **启动抖音** - 仅启动抖音应用
- **测试HID** - 测试HID设备连接状态
- **获取设备信息** - 获取当前设备的屏幕信息
- **保存/加载配置** - 管理配置参数

### UI启动方式
UI界面会在AScript启动时自动显示选择菜单，选择"1. 图形界面模式"即可启动。

## 故障排除

### 常见问题

1. **抖音应用启动失败**
   - 确保设备已安装抖音轻量版
   - 检查应用包名是否正确
   - 尝试手动启动应用一次

2. **HID设备连接失败**
   - 检查ESP32固件是否正确刷入
   - 蓝牙设备：确保已配对连接
   - USB设备：确保已授权访问
   - 检查ESP32插件是否正确安装

3. **操作不准确**
   - 检查屏幕分辨率获取是否正确
   - 调整坐标参数
   - 检查滑动速度和时长

### 调试方法
```python
# 获取当前运行的应用
current_app = system.get_foreground_app()
print(f"当前应用: {current_app}")

# 获取设备信息
screen_width, screen_height = get_device_info()
print(f"屏幕分辨率: {screen_width} x {screen_height}")
```

## 参考文档

- [AScript Android API文档](https://www.ascript.cn/docs/android/api/system/)
- [ESP32 HID设备文档](https://www.ascript.cn/docs/android/esp32)
- [AScript官方论坛](http://bbs.ascript.cn)

## 注意事项

1. 请遵守相关法律法规，合理使用自动化脚本
2. 建议在测试环境中先验证脚本功能
3. 使用HID设备时注意设备兼容性
4. 定期更新固件和插件版本

## 版本信息

- 脚本版本：1.2
- 支持的AScript版本：最新版
- ESP32固件版本：4.1.2
- 最新更新：图片模板匹配、智能重试机制、观看时间优化

## 联系方式

如有问题，请参考AScript官方文档或加入官方QQ群：
- Android研发QQ群：718969991
- HID硬件咨询QQ群：877930261
