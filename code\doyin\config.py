#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
抖音自动化脚本配置文件
用户可以在此文件中自定义各种参数
"""

# ==================== 应用配置 ====================

# 抖音应用包名（可以修改为其他版本）
DOUYIN_PACKAGE_NAMES = [
    "com.ss.android.ugc.aweme.lite",  # 抖音轻量版（默认）
    "com.ss.android.ugc.aweme",       # 抖音正式版
    "com.ss.android.ugc.live"         # 抖音直播版
]

# 应用启动等待时间（秒）
APP_LAUNCH_WAIT_TIME = 5

# 操作间隔时间（秒）
OPERATION_INTERVAL = 2

# ==================== HID设备配置 ====================

# HID设备连接超时时间（秒）
HID_CONNECTION_TIMEOUT = 10

# HID操作默认持续时间（毫秒）
HID_CLICK_DURATION = 50
HID_SLIDE_DURATION = 500

# 蓝牙HID设备配置
BLUETOOTH_HID_CONFIG = {
    "easing_mode": 0,        # 缓动模式: 0=匀速, 1=加速, 2=减速, 3=加减速
    "easing_power": 1,       # 缓动力度
    "report_interval": 30,   # 报文发送间隔
    "down_dur": 0,          # 按下时长
    "up_dur": 0             # 抬起时长
}

# USB HID设备配置
USB_HID_CONFIG = {
    "down_dur": 10,         # 按下时长
    "up_dur": 10,           # 抬起时长
    "step_duration": 3      # 每步耗时
}

# ==================== 屏幕坐标配置 ====================

# 默认屏幕分辨率（当无法获取时使用）
DEFAULT_SCREEN_WIDTH = 1080
DEFAULT_SCREEN_HEIGHT = 2340

# 操作区域比例配置（相对于屏幕尺寸的比例）
SCREEN_REGIONS = {
    "center": (0.5, 0.5),           # 屏幕中央
    "like_button": (0.9, 0.67),     # 点赞按钮
    "comment_button": (0.9, 0.75),  # 评论按钮
    "share_button": (0.9, 0.83),    # 分享按钮
    "follow_button": (0.9, 0.58),   # 关注按钮
    "user_avatar": (0.1, 0.85),     # 用户头像
    "video_title": (0.1, 0.75)      # 视频标题区域
}

# 滑动区域配置
SWIPE_REGIONS = {
    "up_swipe": {
        "start": (0.5, 0.75),       # 向上滑动起点
        "end": (0.5, 0.25)          # 向上滑动终点
    },
    "down_swipe": {
        "start": (0.5, 0.25),       # 向下滑动起点
        "end": (0.5, 0.75)          # 向下滑动终点
    },
    "left_swipe": {
        "start": (0.75, 0.5),       # 向左滑动起点
        "end": (0.25, 0.5)          # 向左滑动终点
    },
    "right_swipe": {
        "start": (0.25, 0.5),       # 向右滑动起点
        "end": (0.75, 0.5)          # 向右滑动终点
    }
}

# ==================== 操作配置 ====================

# 自动化操作配置已移除
# 如需要可以手动配置单独的操作

# ==================== 调试配置 ====================

# 是否启用调试模式
DEBUG_MODE = True

# 是否显示详细日志
VERBOSE_LOGGING = True

# 是否在操作前显示坐标
SHOW_COORDINATES = True

# 是否在操作后截图（需要权限）
TAKE_SCREENSHOTS = False

# ==================== 安全配置 ====================

# 操作频率限制（防止过于频繁的操作）
MIN_OPERATION_INTERVAL = 0.5  # 最小操作间隔（秒）
MAX_OPERATIONS_PER_MINUTE = 60  # 每分钟最大操作次数

# 异常处理配置
MAX_RETRY_ATTEMPTS = 3  # 最大重试次数
RETRY_DELAY = 2  # 重试延迟（秒）

# ==================== 辅助函数 ====================

def get_screen_position(region_name, screen_width, screen_height):
    """根据区域名称获取屏幕坐标"""
    if region_name in SCREEN_REGIONS:
        ratio_x, ratio_y = SCREEN_REGIONS[region_name]
        x = int(screen_width * ratio_x)
        y = int(screen_height * ratio_y)
        return x, y
    else:
        raise ValueError(f"未知的屏幕区域: {region_name}")

def get_swipe_positions(direction, screen_width, screen_height):
    """根据滑动方向获取起止坐标"""
    swipe_key = f"{direction}_swipe"
    if swipe_key in SWIPE_REGIONS:
        start_ratio = SWIPE_REGIONS[swipe_key]["start"]
        end_ratio = SWIPE_REGIONS[swipe_key]["end"]
        
        start_x = int(screen_width * start_ratio[0])
        start_y = int(screen_height * start_ratio[1])
        end_x = int(screen_width * end_ratio[0])
        end_y = int(screen_height * end_ratio[1])
        
        return (start_x, start_y), (end_x, end_y)
    else:
        raise ValueError(f"未知的滑动方向: {direction}")

def get_hid_config(device_type):
    """获取HID设备配置"""
    if device_type == "bluetooth":
        return BLUETOOTH_HID_CONFIG
    elif device_type == "usb":
        return USB_HID_CONFIG
    else:
        return {}

def print_config_info():
    """打印配置信息"""
    print("📋 当前配置信息:")
    print(f"  默认抖音包名: {DOUYIN_PACKAGE_NAMES[0]}")
    print(f"  应用启动等待时间: {APP_LAUNCH_WAIT_TIME}秒")
    print(f"  操作间隔时间: {OPERATION_INTERVAL}秒")
    print(f"  默认屏幕分辨率: {DEFAULT_SCREEN_WIDTH}x{DEFAULT_SCREEN_HEIGHT}")
    print(f"  调试模式: {'开启' if DEBUG_MODE else '关闭'}")
    print(f"  详细日志: {'开启' if VERBOSE_LOGGING else '关闭'}")

if __name__ == "__main__":
    print_config_info()
