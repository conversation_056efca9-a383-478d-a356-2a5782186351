# 功能使用指南

## 🔄 搜索关键词轮换功能

### 功能概述
系统支持三个搜索关键词的自动轮换，每次执行任务时会使用不同的关键词进行搜索。

### 关键词列表
1. **央视新闻** - 新闻类直播，仅观看不点赞
2. **唱歌直播间** - 娱乐类直播，观看并进行大量点赞
3. **购物直播间** - 购物类直播，观看并进行大量点赞

### 轮换机制
- **自动轮换**: 每次搜索后自动切换到下一个关键词
- **循环使用**: 三个关键词循环使用，确保公平分配
- **状态显示**: 实时显示当前搜索关键词和下次关键词
- **日志记录**: 详细记录每次关键词的使用情况

### 使用效果
- **多样化**: 避免总是搜索同一类型的直播
- **真实性**: 模拟真实用户的多样化搜索行为
- **平衡性**: 确保不同类型直播的均衡访问

## 👍 智能点赞功能

### 功能概述
在特定类型的直播间（唱歌直播间和购物直播间）自动进行大量随机点赞，模拟真实用户的互动行为。

### 点赞条件
- **触发条件**: 仅在"唱歌直播间"和"购物直播间"中激活
- **跳过条件**: 在"央视新闻"直播中不进行点赞
- **自动判断**: 系统根据搜索关键词自动判断是否需要点赞

### 点赞参数
- **点赞次数**: 每次随机170-250次，确保超过170次的要求
- **时间分布**: 在10-15分钟观看时间内随机分布
- **点赞区域**: 屏幕右侧中央区域（80%-95%宽度，40%-60%高度）
- **点击速度**: 30ms快速点击，提高效率

### 随机化特性
- **时间随机**: 点赞时间点在观看期间随机分布
- **位置随机**: 每次点赞的坐标在指定区域内随机生成
- **次数随机**: 总点赞次数在170-250之间随机
- **间隔随机**: 点赞间隔时间随机化，避免规律性

### 技术实现
- **HID优先**: 优先使用HID设备进行快速点赞
- **备用方案**: HID不可用时使用普通点击
- **错误处理**: 完善的异常处理，确保程序稳定
- **实时统计**: 实时显示点赞进度和总数

## 📊 观看体验优化

### 实时状态显示
- **观看进度**: 显示已观看时间和剩余时间
- **点赞统计**: 实时显示当前点赞次数
- **目标进度**: 显示点赞目标和完成进度
- **关键词信息**: 显示当前搜索的关键词类型

### 智能分配算法
- **时间分配**: 根据剩余观看时间智能分配点赞次数
- **均匀分布**: 确保点赞在整个观看期间均匀分布
- **动态调整**: 根据实际进度动态调整点赞频率
- **完成保证**: 确保在观看结束前完成所有点赞

### 完成统计
- **观看总结**: 显示总观看时间
- **点赞总结**: 显示总点赞次数和目标完成情况
- **效率统计**: 显示点赞效率和时间分布
- **下次预告**: 显示下次将使用的搜索关键词

## 🎯 使用场景

### 场景1: 央视新闻直播
- **搜索关键词**: "央视新闻"
- **观看时长**: 10-15分钟随机
- **点赞行为**: 不进行点赞
- **适用场景**: 需要观看新闻类内容时

### 场景2: 唱歌直播间
- **搜索关键词**: "唱歌直播间"
- **观看时长**: 10-15分钟随机
- **点赞行为**: 170-250次随机点赞
- **适用场景**: 需要在娱乐直播中进行互动时

### 场景3: 购物直播间
- **搜索关键词**: "购物直播间"
- **观看时长**: 10-15分钟随机
- **点赞行为**: 170-250次随机点赞
- **适用场景**: 需要在购物直播中进行互动时

## ⚙️ 配置说明

### 关键词配置
```python
# 在类初始化中设置
self.search_keywords = ["央视新闻", "唱歌直播间", "购物直播间"]
self.current_keyword_index = 0
```

### 点赞配置
```python
# 点赞次数范围
like_target = random.randint(170, 250)

# 点赞区域设置
like_area_x_min = int(width * 0.8)   # 屏幕右侧20%区域
like_area_x_max = int(width * 0.95)
like_area_y_min = int(height * 0.4)  # 屏幕中央区域
like_area_y_max = int(height * 0.6)
```

### 时间配置
```python
# 观看时间设置
watch_duration = random.randint(10, 15)  # 10-15分钟

# 点击速度设置
click_duration = 30  # 30ms快速点击
```

## 🔧 调试和监控

### 日志级别
- **info**: 一般信息，如关键词切换、观看进度
- **success**: 成功操作，如点赞完成、观看结束
- **warning**: 警告信息，如HID设备不可用
- **error**: 错误信息，如点赞失败、坐标获取失败
- **debug**: 调试信息，如坐标计算、时间分布

### 监控指标
- **关键词轮换**: 监控关键词是否正常轮换
- **点赞效率**: 监控点赞成功率和速度
- **时间分布**: 监控点赞时间分布是否合理
- **设备状态**: 监控HID设备连接状态

### 故障排除
1. **点赞不生效**: 检查HID设备连接，确认点赞区域设置
2. **关键词不轮换**: 检查索引更新逻辑，确认变量初始化
3. **点赞次数不足**: 检查随机数生成，确认目标设置
4. **时间分布不均**: 检查算法逻辑，确认时间计算

## 📈 性能优化建议

### 设备优化
- **使用HID设备**: 获得最佳点赞效果和速度
- **稳定连接**: 确保HID设备连接稳定
- **设备兼容**: 选择兼容性好的ESP32设备

### 参数调整
- **点赞区域**: 根据实际界面调整点赞区域
- **点击速度**: 根据设备性能调整点击速度
- **点赞次数**: 根据需要调整点赞次数范围

### 监控建议
- **定期检查**: 定期检查功能运行状态
- **日志分析**: 分析日志找出潜在问题
- **效果评估**: 评估点赞效果和用户体验
- **参数优化**: 根据实际效果优化参数设置
