# 图片资源说明

## 需要的图片文件

### 直播中.png
- **用途**: 用于识别直播中的控件并进行点击
- **要求**: 
  - 图片应该包含"直播中"相关的视觉元素
  - 建议截取直播间中明显的"直播中"标识
  - 图片格式: PNG
  - 建议尺寸: 不超过200x200像素
  - 背景应该与实际应用中的背景相匹配

### 赚钱.png
- **用途**: 用于识别赚钱图标并进行点击
- **要求**:
  - 图片应该包含赚钱相关的图标
  - 建议截取应用中的赚钱按钮或图标
  - 图片格式: PNG
  - 建议尺寸: 不超过100x100像素

## 图片获取方法

1. 在目标应用中找到相应的控件
2. 使用截图工具截取控件图片
3. 保存为PNG格式
4. 将图片文件放置在此目录下

## 注意事项

- 图片质量会直接影响识别准确率
- 建议在不同光线条件下测试识别效果
- 如果识别率不高，可以尝试调整confidence参数（当前设置为0.95）
