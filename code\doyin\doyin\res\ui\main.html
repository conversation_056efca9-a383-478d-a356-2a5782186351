<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8" />
    <meta
      name="viewport"
      content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no"
    />
    <title>抖音自动化控制台</title>
    
    <!-- LayUI 移动端 CSS -->
    <link
      rel="stylesheet"
      href="https://unpkg.com/layui@2.8.18/dist/css/layui.css"
    />
    
    <style>
        body {
            margin: 0;
            padding: 10px;
        font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto,
          sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        
        .container {
            max-width: 100%;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 15px;
            padding: 20px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            backdrop-filter: blur(10px);
        }
        
        .header {
            text-align: center;
            margin-bottom: 20px;
            padding-bottom: 15px;
            border-bottom: 2px solid #f0f0f0;
        }
        
        .header h1 {
            margin: 0;
            color: #333;
            font-size: 24px;
            font-weight: 600;
        }
        
        .status-card {
        background: linear-gradient(45deg, #4caf50, #45a049);
            color: white;
            padding: 15px;
            border-radius: 10px;
            margin-bottom: 20px;
            text-align: center;
            box-shadow: 0 4px 15px rgba(76, 175, 80, 0.3);
        }
        
        .status-text {
            font-size: 18px;
            font-weight: 500;
            margin: 0;
        }
        
        .control-panel {
            margin-bottom: 20px;
        }
        
        .btn-group {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 10px;
            margin-bottom: 15px;
            align-items: center;
        }

        .btn-full {
            grid-column: 1 / -1;
        }

        .layui-btn {
            border-radius: 8px;
            font-weight: 500;
            padding: 12px 16px;
            border: none;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            text-align: center;
            min-height: 48px;
            line-height: 1.4;
            white-space: nowrap;
            box-sizing: border-box;
        }
        
        .layui-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
        }

        .layui-btn i {
            margin-right: 6px;
            font-size: 16px;
        }

        .layui-btn-fluid {
            width: 100%;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        /* 确保所有按钮都有统一的高度和对齐 */
        .btn-group .layui-btn {
            height: 48px;
            min-height: 48px;
            max-height: 48px;
        }

        /* 小屏幕优化 */
        @media (max-width: 360px) {
            .layui-btn {
                padding: 10px 12px;
                font-size: 13px;
                min-height: 44px;
            }

            .btn-group .layui-btn {
                height: 44px;
                min-height: 44px;
                max-height: 44px;
            }
        }
        
        .info-card {
            background: white;
            border-radius: 10px;
            padding: 15px;
            margin-bottom: 15px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }
        
        .info-title {
            font-weight: 600;
            color: #333;
            margin-bottom: 10px;
            font-size: 16px;
        }
        
        .info-content {
            color: #666;
            font-size: 14px;
            line-height: 1.5;
        }
        
        .log-container {
            background: #1e1e1e;
            border-radius: 10px;
            padding: 15px;
            max-height: 300px;
            overflow-y: auto;
        font-family: "Courier New", monospace;
            font-size: 12px;
        }
        
        .log-entry {
            margin-bottom: 5px;
            padding: 5px;
            border-radius: 4px;
        }
        
      .log-info {
        color: #4caf50;
      }
      .log-warning {
        color: #ff9800;
      }
      .log-error {
        color: #f44336;
      }
      .log-success {
        color: #00e676;
      }
      .log-debug {
        color: #9e9e9e;
      }
        
        .hid-status {
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .status-indicator {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            background: #f44336;
        }
        
        .status-indicator.connected {
        background: #4caf50;
        }
        
        .config-section {
            margin-top: 20px;
        }
        
        .layui-form-item {
            margin-bottom: 15px;
        }
        
      .layui-input,
      .layui-select {
            border-radius: 6px;
            border: 1px solid #ddd;
            padding: 10px;
        }
        
        .tabs {
            margin-bottom: 20px;
        }
        
        .tab-content {
            display: none;
        }
        
        .tab-content.active {
            display: block;
        }
        
        @media (max-width: 480px) {
            .container {
                padding: 15px;
                margin: 5px;
            }

            .btn-group {
                grid-template-columns: 1fr;
                gap: 12px;
            }

            .header h1 {
                font-size: 20px;
            }

            .layui-btn {
                padding: 14px 16px;
                min-height: 50px;
                font-size: 15px;
            }

            .btn-group .layui-btn {
                height: 50px;
                min-height: 50px;
                max-height: 50px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- 头部 -->
        <div class="header">
            <h1>🎬 抖音自动化控制台</h1>
            <div class="status-card">
                <p class="status-text" id="statusText">待机中...</p>
            </div>
        </div>
        
        <!-- 标签页 -->
        <div class="layui-tab layui-tab-brief tabs" lay-filter="mainTabs">
            <ul class="layui-tab-title">
                <li class="layui-this">控制面板</li>
                <li>设备信息</li>
                <li>配置设置</li>
                <li>运行日志</li>
            </ul>
            
            <div class="layui-tab-content">
                <!-- 控制面板 -->
                <div class="layui-tab-item layui-show">
                    <div class="control-panel">
                        <div class="btn-group">
                <button
                  class="layui-btn layui-btn-danger"
                  onclick="stopAutomation()"
                >
                                <i class="layui-icon layui-icon-pause"></i> 停止运行
                            </button>
                <button
                  class="layui-btn layui-btn-normal"
                  onclick="launchDouyin()"
                >
                                <i class="layui-icon layui-icon-app"></i> 启动抖音
                            </button>
                            <button class="layui-btn layui-btn-warm" onclick="testHid()">
                                <i class="layui-icon layui-icon-bluetooth"></i> 测试HID
                            </button>
                        </div>
                        
              <button
                class="layui-btn layui-btn-fluid btn-full"
                onclick="getDeviceInfo()"
              >
                            <i class="layui-icon layui-icon-cellphone"></i> 获取设备信息
                        </button>
                    </div>
                    
                    <!-- HID设备状态 -->
                    <div class="info-card">
                        <div class="info-title">HID设备状态</div>
                        <div class="hid-status">
                            <div class="status-indicator" id="hidIndicator"></div>
                            <span id="hidStatusText">连接中...</span>
                        </div>
              <div style="margin-top: 10px">
                <button
                  class="layui-btn layui-btn-xs layui-btn-normal"
                  onclick="reconnectHid()"
                >
                                <i class="layui-icon layui-icon-refresh"></i> 重新连接
                            </button>
                        </div>
                    </div>
                </div>
                
                <!-- 设备信息 -->
                <div class="layui-tab-item">
                    <div class="info-card">
                        <div class="info-title">屏幕信息</div>
                        <div class="info-content" id="deviceInfo">
                            点击"获取设备信息"按钮获取当前设备信息
                        </div>
                    </div>
                    
                    <div class="info-card">
                        <div class="info-title">应用信息</div>
                        <div class="info-content">
                            <p><strong>目标应用:</strong> 抖音轻量版</p>
                            <p><strong>包名:</strong> com.ss.android.ugc.aweme.lite</p>
                            <p><strong>支持版本:</strong> 所有版本</p>
                        </div>
                    </div>
                </div>
                
                <!-- 配置设置 -->
                <div class="layui-tab-item">
                    <form class="layui-form config-section" lay-filter="configForm">
                        <div class="layui-form-item">
                            <label class="layui-form-label">操作间隔</label>
                            <div class="layui-input-block">
                  <input
                    type="number"
                    name="interval"
                    placeholder="秒"
                    class="layui-input"
                    value="2"
                  />
                            </div>
                        </div>
                        
                        <div class="layui-form-item">
                            <label class="layui-form-label">启动等待</label>
                            <div class="layui-input-block">
                  <input
                    type="number"
                    name="launchWait"
                    placeholder="秒"
                    class="layui-input"
                    value="5"
                  />
                            </div>
                        </div>
                        
                        <div class="layui-form-item">
                            <label class="layui-form-label">点击时长</label>
                            <div class="layui-input-block">
                  <input
                    type="number"
                    name="clickDuration"
                    placeholder="毫秒"
                    class="layui-input"
                    value="50"
                  />
                            </div>
                        </div>
                        
                        <div class="layui-form-item">
                            <label class="layui-form-label">滑动时长</label>
                            <div class="layui-input-block">
                  <input
                    type="number"
                    name="slideDuration"
                    placeholder="毫秒"
                    class="layui-input"
                    value="500"
                  />
                            </div>
                        </div>
                        
                        <div class="layui-form-item">
                            <div class="layui-input-block">
                  <button
                    type="button"
                    class="layui-btn"
                    onclick="saveConfig()"
                  >
                    保存配置
                  </button>
                  <button
                    type="button"
                    class="layui-btn layui-btn-primary"
                    onclick="loadConfig()"
                  >
                    加载配置
                  </button>
                            </div>
                        </div>
                    </form>
                </div>
                
                <!-- 运行日志 -->
                <div class="layui-tab-item">
            <div style="margin-bottom: 10px">
              <button
                class="layui-btn layui-btn-sm layui-btn-danger"
                onclick="clearLogs()"
              >
                            <i class="layui-icon layui-icon-delete"></i> 清空日志
                        </button>
                    </div>
                    <div class="log-container" id="logContainer">
                        <div class="log-entry log-info">[系统] 日志系统已启动</div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 底部按钮 -->
      <div
        style="
          text-align: center;
          margin-top: 20px;
          display: flex;
          gap: 10px;
          justify-content: center;
        "
      >
            <button class="layui-btn layui-btn-normal" onclick="minimizeWindow()">
                <i class="layui-icon layui-icon-shrink-right"></i> 最小化
            </button>
            <button class="layui-btn layui-btn-primary" onclick="closeWindow()">
                <i class="layui-icon layui-icon-close"></i> 关闭窗口
            </button>
        </div>
    </div>
    
    <!-- LayUI JS -->
    <script src="https://unpkg.com/layui@2.8.18/dist/layui.js"></script>
    
    <script>
        // LayUI 初始化
      layui.use(["element", "form", "layer"], function () {
            var element = layui.element;
            var form = layui.form;
            var layer = layui.layer;
            
            // 表单渲染
            form.render();
        });
        
        // 控制函数
        function stopAutomation() {
        window.airscript.call("stop_automation", "true");
        updateStatus("停止中...");
        }
        
        function testHid() {
        window.airscript.call("test_hid", "true");
        updateStatus("测试HID连接...");
        }
        
        function launchDouyin() {
        window.airscript.call("launch_douyin", "true");
        updateStatus("启动抖音...");

            // 显示用户友好的提示
        if (typeof layui !== "undefined" && layui.layer) {
          layui.layer.msg(
            "正在启动抖音，查找赚钱图标并点击奖励控件，自动监控金币增加状态，界面将立即隐藏并显示悬浮窗",
            {
                    icon: 1,
              time: 4000,
            }
          );
            }
        }
        
        function getDeviceInfo() {
        window.airscript.call("get_device_info", "true");
        }
        
        function saveConfig() {
            var config = {
                interval: document.querySelector('input[name="interval"]').value,
                launchWait: document.querySelector('input[name="launchWait"]').value,
          clickDuration: document.querySelector('input[name="clickDuration"]')
            .value,
          slideDuration: document.querySelector('input[name="slideDuration"]')
            .value,
            };
        window.airscript.call("save_config", JSON.stringify(config));
        }
        
        function loadConfig() {
        window.airscript.call("load_config", "true");
        }
        
        function clearLogs() {
        var container = document.getElementById("logContainer");
            if (container) {
          container.innerHTML = "";
            }
        window.airscript.call("clear_logs", "true");
        }
        
        function closeWindow() {
        window.airscript.call("close_window", "true");
        }

        function reconnectHid() {
        window.airscript.call("reconnect_hid", "true");
        updateStatus("重新连接HID设备...");
        }
        
        // 更新状态
        function updateStatus(status) {
        var statusElement = document.getElementById("statusText");
            if (statusElement) {
                statusElement.textContent = status;
            }
            // 同时更新最小化界面的状态
        var miniStatus = document.getElementById("miniStatus");
            if (miniStatus) {
                miniStatus.textContent = status;
            }
        }
        
        // 更新日志
        function updateLog(logEntry) {
        var container = document.getElementById("logContainer");
            if (!container) {
                // 容器不存在时跳过更新（可能界面已最小化）
                return;
            }
        var logDiv = document.createElement("div");
        logDiv.className = "log-entry log-" + logEntry.level;
        logDiv.innerHTML = "[" + logEntry.time + "] " + logEntry.message;
            container.appendChild(logDiv);
            container.scrollTop = container.scrollHeight;
        }
        
        // 更新HID状态
        function updateHidStatus(deviceInfo) {
        var indicator = document.getElementById("hidIndicator");
        var statusText = document.getElementById("hidStatusText");

            // 检查元素是否存在
            if (!indicator || !statusText) {
                return;
            }

            if (deviceInfo.connected) {
          indicator.classList.add("connected");
          statusText.textContent =
            deviceInfo.type + " - " + (deviceInfo.name || "已连接");
            } else {
          indicator.classList.remove("connected");
          statusText.textContent = "未连接";
            }
        }
        
        // 更新设备信息
        function updateDeviceInfo(deviceInfo) {
        var infoDiv = document.getElementById("deviceInfo");
            if (!infoDiv) {
                return;
            }
            infoDiv.innerHTML =
          "<p><strong>屏幕分辨率:</strong> " +
          deviceInfo.width +
          " x " +
          deviceInfo.height +
          "</p>" +
          "<p><strong>屏幕比例:</strong> " +
          deviceInfo.ratio +
          "</p>" +
          "<p><strong>更新时间:</strong> " +
          new Date().toLocaleTimeString() +
          "</p>";
        }
        
        // 加载配置到表单
        function loadConfigData(configData) {
        if (configData && typeof configData === "object") {
                if (configData.interval) {
            document.querySelector('input[name="interval"]').value =
              configData.interval;
                }
                if (configData.launchWait) {
            document.querySelector('input[name="launchWait"]').value =
              configData.launchWait;
                }
                if (configData.clickDuration) {
            document.querySelector('input[name="clickDuration"]').value =
              configData.clickDuration;
                }
                if (configData.slideDuration) {
            document.querySelector('input[name="slideDuration"]').value =
              configData.slideDuration;
                }
                // 配置加载成功，通过Python端日志显示
            } else {
                // 配置为空，通过Python端日志显示
            }
        }
        
        // 页面加载完成后的初始化
      document.addEventListener("DOMContentLoaded", function () {
            // 页面加载完成，通过Python端日志显示

            // 通知Python端页面已加载完成
            if (window.airscript) {
                try {
            window.airscript.call("page_loaded", "true");
                } catch (e) {
                    // 通知失败，静默处理
                }
            }
        });

        // 最小化和恢复界面切换
        function switchToMinimizedView() {
            document.body.innerHTML = `
                <div style="
                    width: 100%;
                    height: 100vh;
                    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                    display: flex;
                    flex-direction: column;
                    align-items: center;
                    justify-content: center;
                    border-radius: 15px;
                    box-shadow: 0 8px 32px rgba(0,0,0,0.3);
                    position: relative;
                    overflow: hidden;
                ">
                    <!-- 装饰性背景图案 -->
                    <div style="
                        position: absolute;
                        top: -20px;
                        right: -20px;
                        width: 40px;
                        height: 40px;
                        background: rgba(255,255,255,0.1);
                        border-radius: 50%;
                    "></div>
                    <div style="
                        position: absolute;
                        bottom: -15px;
                        left: -15px;
                        width: 30px;
                        height: 30px;
                        background: rgba(255,255,255,0.08);
                        border-radius: 50%;
                    "></div>

                    <!-- 主图标 -->
                    <div style="
                        width: 36px;
                        height: 36px;
                        background: rgba(255,255,255,0.2);
                        border-radius: 12px;
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        margin-bottom: 8px;
                        backdrop-filter: blur(10px);
                        border: 1px solid rgba(255,255,255,0.3);
                        cursor: pointer;
                    " onclick="window.airscript.call('restore_window', 'true')">
                        <div style="
                            font-size: 18px;
                            color: white;
                            font-weight: bold;
                        ">📱</div>
                    </div>

                    <!-- 状态指示器 -->
                    <div style="
                        width: 8px;
                        height: 8px;
                        background: #4CAF50;
                        border-radius: 50%;
                        margin-bottom: 6px;
                        animation: pulse 2s infinite;
                        box-shadow: 0 0 8px rgba(76, 175, 80, 0.6);
                    "></div>

                    <!-- 状态文本 -->
                    <div id="miniStatus" style="
                        font-size: 8px;
                        color: rgba(255,255,255,0.8);
                        text-align: center;
                        font-weight: 500;
                        margin-bottom: 8px;
                    ">运行中</div>

                    <!-- 隐藏的日志容器 -->
                    <div id="logContainer" style="display: none;"></div>

                    <!-- 控制按钮 -->
                    <div style="display: flex; flex-direction: column; gap: 4px;">
                        <button onclick="window.airscript.call('stop_automation', 'true')" style="
                            width: 24px;
                            height: 24px;
                            background: rgba(220, 53, 69, 0.8);
                            color: white;
                            border: none;
                            border-radius: 6px;
                            cursor: pointer;
                            font-size: 10px;
                            display: flex;
                            align-items: center;
                            justify-content: center;
                            backdrop-filter: blur(10px);
                            transition: all 0.3s ease;
                        " onmouseover="this.style.background='rgba(220, 53, 69, 1)'"
                           onmouseout="this.style.background='rgba(220, 53, 69, 0.8)'">⏹</button>
                    </div>

                    <style>
                        @keyframes pulse {
                            0% { opacity: 1; transform: scale(1); }
                            50% { opacity: 0.7; transform: scale(1.1); }
                            100% { opacity: 1; transform: scale(1); }
                        }
                    </style>
                </div>
            `;

            // 重新定义必要的全局函数
        window.updateLog = function (logEntry) {
          var container = document.getElementById("logContainer");
                if (!container) {
                    return;
                }
          var logDiv = document.createElement("div");
          logDiv.className = "log-entry log-" + logEntry.level;
          logDiv.innerHTML = "[" + logEntry.time + "] " + logEntry.message;
                container.appendChild(logDiv);
                container.scrollTop = container.scrollHeight;
            };

        window.updateStatus = function (status) {
          var miniStatus = document.getElementById("miniStatus");
                if (miniStatus) {
                    miniStatus.textContent = status;
                }
            };
        }

        function switchToNormalView() {
            location.reload(); // 重新加载完整界面
        }

        function restoreWindow() {
        window.airscript.call("restore_window", "true");
        }

        function minimizeWindow() {
        window.airscript.call("minimize_window", "true");
        }

        // 显示任务完成消息
        function showCompletionMessage(messageData) {
            try {
                if (typeof layui !== "undefined" && layui.layer) {
                    // 使用LayUI显示成功消息
                    layui.layer.alert(messageData.message, {
                        icon: 1,
                        title: messageData.title,
                        skin: 'layui-layer-molv',
                        closeBtn: 1,
                        anim: 2,
                        btn: ['确定'],
                        yes: function(index) {
                            layui.layer.close(index);
                        }
                    });
                } else {
                    // 备用方案：使用原生alert
                    alert(messageData.title + "\n\n" + messageData.message.replace(/\\n/g, '\n'));
                }
            } catch (e) {
                console.error("显示完成消息失败:", e);
                // 最后的备用方案
                alert("任务完成！\n\n已成功点击'回任务页'按钮\n程序执行完毕");
            }
        }

        // 确保函数在全局作用域中可用
        window.updateLog = updateLog;
        window.updateStatus = updateStatus;
        window.updateHidStatus = updateHidStatus;
        window.updateDeviceInfo = updateDeviceInfo;
        window.loadConfigData = loadConfigData;
        window.switchToMinimizedView = switchToMinimizedView;
        window.switchToNormalView = switchToNormalView;
        window.showCompletionMessage = showCompletionMessage;
    </script>
</body>
</html>
