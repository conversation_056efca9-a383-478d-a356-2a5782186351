# 图片模板匹配更新记录

## 更新时间
2024年12月19日

## 更新内容

### 主要修改
将央视一套/央视网控件查找方式改为使用图片模板匹配，提高识别准确性和稳定性。

### 具体变更

#### 1. 修改控件查找逻辑
**原来的方式：**
```python
# 查找包含"央视一套"文本的控件的父级控件
cctv_element = Selector().text("央视一套").type("LynxFlattenUI").parent(1).find()

# 备用方案1：尝试查找央视网控件
cctv_net_element = Selector().text("央视网").type("FlattenUIText").find()

# 备用方案2：尝试原来的直播区域控件
live_area = Selector().id("top_mask_without_gradient").type("View").packageName("com.ss.android.ugc.aweme.search_lite_plugin").parent(1).find()
```

**新的方式：**
```python
# 使用FindImages查找直播中图片
results = FindImages.find_all_template([R.img("直播中.png")], confidence=0.95)
```

#### 2. 坐标解析优化
新增了多种坐标解析方式，支持不同格式的图像识别结果：
- 字典格式：`{'x': x, 'y': y}` 或 `{'center_x': x, 'center_y': y}`
- 边界格式：`{'left': x1, 'top': y1, 'right': x2, 'bottom': y2}`
- 列表/元组格式：`[x, y]` 或 `(x, y)`
- 对象属性格式：`result.x, result.y` 或 `result.center_x, result.center_y`

#### 3. 备用方案保留
如果图片识别失败，仍然会使用原来的控件查找方式作为备用方案。

### 文件修改列表

#### 1. 主要代码文件
- `doyin/__init__.py` (第1508-1593行)
  - 修改了步骤6的控件查找逻辑
  - 将文本控件查找改为图片模板匹配
  - 优化了坐标解析逻辑

#### 2. 新增资源文件
- `doyin/res/img/README.md` - 图片资源说明文档
- `doyin/res/img/直播中.png.placeholder` - 占位符文件

### 使用说明

#### 1. 准备图片资源
用户需要提供 `直播中.png` 图片文件：
1. 在目标应用中找到"直播中"相关的视觉元素
2. 截取包含该元素的图片
3. 保存为PNG格式
4. 将文件命名为 `直播中.png`
5. 放置在 `doyin/res/img/` 目录下
6. 删除 `直播中.png.placeholder` 占位符文件

#### 2. 图片要求
- **格式**: PNG
- **尺寸**: 建议不超过200x200像素
- **内容**: 包含"直播中"相关的视觉元素
- **背景**: 应与实际应用中的背景相匹配
- **质量**: 清晰度要足够，避免模糊

#### 3. 识别参数
- **置信度**: 当前设置为0.95（95%匹配度）
- **查找方式**: `find_all_template` 查找所有匹配项
- **点击策略**: 点击找到的第一个匹配项

### 优势

#### 1. 提高稳定性
- 图片识别不依赖于文本内容变化
- 对界面布局变化有更好的适应性
- 减少因控件属性变化导致的识别失败

#### 2. 提高准确性
- 视觉识别更接近人眼识别方式
- 可以识别图标、按钮等非文本元素
- 支持复杂的视觉元素识别

#### 3. 更好的维护性
- 图片资源可以独立更新
- 不需要修改代码即可适配界面变化
- 便于调试和测试

### 注意事项

#### 1. 图片质量要求
- 图片质量直接影响识别准确率
- 建议在不同光线条件下测试
- 如果识别率不高，可以调整confidence参数

#### 2. 性能考虑
- 图片识别比文本控件查找稍慢
- 建议使用适当大小的图片
- 可以根据需要调整识别区域

#### 3. 兼容性
- 保留了原有的备用方案
- 确保在图片识别失败时仍能正常工作
- 向后兼容现有的功能

### 测试建议

1. **准备测试图片**
   - 在不同设备上截取"直播中"图片
   - 测试不同尺寸和质量的图片

2. **测试识别效果**
   - 在不同光线条件下测试
   - 测试不同界面状态下的识别效果

3. **调整参数**
   - 如果识别率不理想，可以调整confidence值
   - 可以尝试不同的图片处理方式

### 后续优化方向

1. **多图片支持**
   - 支持多个不同的"直播中"图片
   - 适应不同的界面状态

2. **动态参数调整**
   - 根据识别效果自动调整confidence
   - 支持用户自定义识别参数

3. **图片管理工具**
   - 提供图片截取和管理工具
   - 自动化图片质量检测
