<!DOCTYPE html>
<html lang="zh-CN">
  <head>
    <meta charset="UTF-8" />
    <meta
      name="viewport"
      content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no"
    />
    <title>抖音自动化控制台</title>

    <style>
      * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
      }

      body {
        font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto,
          sans-serif;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        min-height: 100vh;
        padding: 10px;
      }

      .container {
        max-width: 100%;
        margin: 0 auto;
        background: rgba(255, 255, 255, 0.95);
        border-radius: 15px;
        padding: 20px;
        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
      }

      .header {
        text-align: center;
        margin-bottom: 20px;
        padding-bottom: 15px;
        border-bottom: 2px solid #f0f0f0;
      }

      .header h1 {
        color: #333;
        font-size: 24px;
        margin-bottom: 10px;
      }

      .status {
        background: linear-gradient(45deg, #4caf50, #45a049);
        color: white;
        padding: 15px;
        border-radius: 10px;
        text-align: center;
        font-size: 16px;
        font-weight: 500;
      }

      .btn-grid {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 10px;
        margin: 20px 0;
        align-items: center;
      }

      .btn-full {
        grid-column: 1 / -1;
      }

      .btn {
        padding: 12px 16px;
        border: none;
        border-radius: 8px;
        font-size: 14px;
        font-weight: 500;
        cursor: pointer;
        transition: all 0.3s ease;
        text-align: center;
        display: flex;
        align-items: center;
        justify-content: center;
        min-height: 48px;
        line-height: 1.4;
        white-space: nowrap;
        box-sizing: border-box;
      }

      .btn:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
      }

      .btn span {
        margin-left: 6px;
      }

      /* 确保所有按钮都有统一的高度和对齐 */
      .btn-grid .btn {
        height: 48px;
        min-height: 48px;
        max-height: 48px;
      }

      /* 小屏幕优化 */
      @media (max-width: 360px) {
        .btn {
          padding: 10px 12px;
          font-size: 13px;
          min-height: 44px;
        }

        .btn-grid .btn {
          height: 44px;
          min-height: 44px;
          max-height: 44px;
        }
      }

      .btn-primary {
        background: #007bff;
        color: white;
      }

      .btn-danger {
        background: #dc3545;
        color: white;
      }

      .btn-success {
        background: #28a745;
        color: white;
      }

      .btn-warning {
        background: #ffc107;
        color: #212529;
      }

      .btn-secondary {
        background: #6c757d;
        color: white;
      }

      .info-card {
        background: white;
        border-radius: 10px;
        padding: 15px;
        margin: 15px 0;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
      }

      .info-title {
        font-weight: 600;
        color: #333;
        margin-bottom: 10px;
        font-size: 16px;
      }

      .info-content {
        color: #666;
        font-size: 14px;
        line-height: 1.5;
      }

      .hid-status {
        display: flex;
        align-items: center;
        gap: 10px;
      }

      .status-dot {
        width: 12px;
        height: 12px;
        border-radius: 50%;
        background: #dc3545;
      }

      .status-dot.connected {
        background: #28a745;
      }

      .log-container {
        background: #1e1e1e;
        border-radius: 10px;
        padding: 15px;
        max-height: 200px;
        overflow-y: auto;
        font-family: "Courier New", monospace;
        font-size: 12px;
        margin: 15px 0;
      }

      .log-entry {
        margin-bottom: 5px;
        padding: 3px;
      }

      .log-info {
        color: #4caf50;
      }
      .log-warning {
        color: #ff9800;
      }
      .log-error {
        color: #f44336;
      }
      .log-success {
        color: #00e676;
      }

      .tabs {
        display: flex;
        margin-bottom: 20px;
        background: #f8f9fa;
        border-radius: 8px;
        padding: 5px;
      }

      .tab {
        flex: 1;
        padding: 10px;
        text-align: center;
        border-radius: 6px;
        cursor: pointer;
        transition: all 0.3s ease;
        font-size: 14px;
      }

      .tab.active {
        background: #007bff;
        color: white;
      }

      .tab-content {
        display: none;
      }

      .tab-content.active {
        display: block;
      }

      .form-group {
        margin-bottom: 15px;
      }

      .form-label {
        display: block;
        margin-bottom: 5px;
        font-weight: 500;
        color: #333;
      }

      .form-input {
        width: 100%;
        padding: 10px;
        border: 1px solid #ddd;
        border-radius: 6px;
        font-size: 14px;
      }

      @media (max-width: 480px) {
        .container {
          padding: 15px;
          margin: 5px;
        }

        .btn-grid {
          grid-template-columns: 1fr;
          gap: 12px;
        }

        .header h1 {
          font-size: 20px;
        }

        .tabs {
          flex-direction: column;
        }

        .btn {
          padding: 14px 16px;
          min-height: 50px;
          font-size: 15px;
        }

        .btn-grid .btn {
          height: 50px;
          min-height: 50px;
          max-height: 50px;
        }
      }
    </style>
  </head>
  <body>
    <div class="container">
      <!-- 头部 -->
      <div class="header">
        <h1>🎬 抖音自动化控制台</h1>
        <div class="status" id="statusDisplay">待机中...</div>
      </div>

      <!-- 标签页 -->
      <div class="tabs">
        <div class="tab active" onclick="showTab('control')">控制</div>
        <div class="tab" onclick="showTab('device')">设备</div>
        <div class="tab" onclick="showTab('config')">配置</div>
        <div class="tab" onclick="showTab('logs')">日志</div>
      </div>

      <!-- 控制面板 -->
      <div id="control" class="tab-content active">
        <div class="btn-grid">
          <button class="btn btn-danger" onclick="stopAutomation()">
            ⏹️ 停止运行
          </button>
          <button class="btn btn-success" onclick="launchDouyin()">
            📱 启动抖音
          </button>
          <button class="btn btn-warning" onclick="testHid()">
            🔌 测试HID
          </button>
          <button class="btn btn-secondary btn-full" onclick="getDeviceInfo()">
            📊 获取设备信息
          </button>
        </div>

        <div class="info-card">
          <div class="info-title">HID设备状态</div>
          <div class="hid-status">
            <div class="status-dot" id="hidDot"></div>
            <span id="hidText">连接中...</span>
          </div>
          <div style="margin-top: 10px">
            <button class="btn btn-sm btn-info" onclick="reconnectHid()">
              🔄 重新连接
            </button>
          </div>
        </div>
      </div>

      <!-- 设备信息 -->
      <div id="device" class="tab-content">
        <div class="info-card">
          <div class="info-title">屏幕信息</div>
          <div class="info-content" id="deviceInfo">
            点击"获取设备信息"按钮获取当前设备信息
          </div>
        </div>

        <div class="info-card">
          <div class="info-title">应用信息</div>
          <div class="info-content">
            <p><strong>目标应用:</strong> 抖音轻量版</p>
            <p><strong>包名:</strong> com.ss.android.ugc.aweme.lite</p>
            <p><strong>支持版本:</strong> 所有版本</p>
          </div>
        </div>
      </div>

      <!-- 配置设置 -->
      <div id="config" class="tab-content">
        <div class="form-group">
          <label class="form-label">操作间隔 (秒)</label>
          <input
            type="number"
            class="form-input"
            id="interval"
            value="2"
            min="1"
            max="10"
          />
        </div>

        <div class="form-group">
          <label class="form-label">启动等待 (秒)</label>
          <input
            type="number"
            class="form-input"
            id="launchWait"
            value="5"
            min="3"
            max="15"
          />
        </div>

        <div class="form-group">
          <label class="form-label">点击时长 (毫秒)</label>
          <input
            type="number"
            class="form-input"
            id="clickDuration"
            value="50"
            min="20"
            max="200"
          />
        </div>

        <div class="form-group">
          <label class="form-label">滑动时长 (毫秒)</label>
          <input
            type="number"
            class="form-input"
            id="slideDuration"
            value="500"
            min="200"
            max="2000"
          />
        </div>

        <div class="btn-grid">
          <button class="btn btn-primary" onclick="saveConfig()">
            💾 保存配置
          </button>
          <button class="btn btn-secondary" onclick="loadConfig()">
            📂 加载配置
          </button>
        </div>
      </div>

      <!-- 运行日志 -->
      <div id="logs" class="tab-content">
        <button
          class="btn btn-danger"
          onclick="clearLogs()"
          style="margin-bottom: 10px"
        >
          🗑️ 清空日志
        </button>
        <div class="log-container" id="logContainer">
          <div class="log-entry log-info">[系统] 日志系统已启动</div>
        </div>
      </div>

      <!-- 底部按钮 -->
      <div
        style="
          text-align: center;
          margin-top: 20px;
          display: flex;
          gap: 10px;
          justify-content: center;
        "
      >
        <button class="btn btn-info" onclick="minimizeWindow()">
          📱 最小化
        </button>
        <button class="btn btn-secondary" onclick="closeWindow()">
          ❌ 关闭窗口
        </button>
      </div>
    </div>

    <script>
      // 标签页切换
      function showTab(tabName) {
        // 隐藏所有标签内容
        var contents = document.querySelectorAll(".tab-content");
        contents.forEach(function (content) {
          content.classList.remove("active");
        });

        // 移除所有标签的active类
        var tabs = document.querySelectorAll(".tab");
        tabs.forEach(function (tab) {
          tab.classList.remove("active");
        });

        // 显示选中的标签内容
        document.getElementById(tabName).classList.add("active");

        // 激活对应的标签
        event.target.classList.add("active");
      }

      // 控制函数
      function stopAutomation() {
        window.airscript.call("stop_automation", "true");
        updateStatus("停止中...");
      }

      function testHid() {
        window.airscript.call("test_hid", "true");
        updateStatus("测试HID连接...");
      }

      function launchDouyin() {
        window.airscript.call("launch_douyin", "true");
        updateStatus("启动抖音...");

        // 显示用户友好的提示
        alert(
          "正在启动抖音，查找赚钱图标并点击奖励控件，自动监控金币增加状态，界面将立即隐藏为悬浮窗"
        );
      }

      function getDeviceInfo() {
        window.airscript.call("get_device_info", "true");
      }

      function saveConfig() {
        var config = {
          interval: document.getElementById("interval").value,
          launchWait: document.getElementById("launchWait").value,
          clickDuration: document.getElementById("clickDuration").value,
          slideDuration: document.getElementById("slideDuration").value,
        };
        window.airscript.call("save_config", JSON.stringify(config));
      }

      function loadConfig() {
        window.airscript.call("load_config", "true");
      }

      function clearLogs() {
        var container = document.getElementById("logContainer");
        if (container) {
          container.innerHTML = "";
        }
        window.airscript.call("clear_logs", "true");
      }

      function closeWindow() {
        window.airscript.call("close_window", "true");
      }

      function reconnectHid() {
        window.airscript.call("reconnect_hid", "true");
        updateStatus("重新连接HID设备...");
      }

      // 更新状态
      function updateStatus(status) {
        var statusElement = document.getElementById("statusDisplay");
        if (statusElement) {
          statusElement.textContent = status;
        }
        // 同时更新最小化界面的状态
        var miniStatus = document.getElementById("miniStatus");
        if (miniStatus) {
          miniStatus.textContent = status;
        }
      }

      // 更新日志
      function updateLog(logEntry) {
        var container = document.getElementById("logContainer");
        if (!container) {
          // 容器不存在时跳过更新（可能界面已最小化）
          return;
        }
        var logDiv = document.createElement("div");
        logDiv.className = "log-entry log-" + logEntry.level;
        logDiv.innerHTML = "[" + logEntry.time + "] " + logEntry.message;
        container.appendChild(logDiv);
        container.scrollTop = container.scrollHeight;
      }

      // 更新HID状态
      function updateHidStatus(deviceInfo) {
        var dot = document.getElementById("hidDot");
        var text = document.getElementById("hidText");

        // 检查元素是否存在
        if (!dot || !text) {
          return;
        }

        if (deviceInfo.connected) {
          dot.classList.add("connected");
          text.textContent =
            deviceInfo.type + " - " + (deviceInfo.name || "已连接");
        } else {
          dot.classList.remove("connected");
          text.textContent = "未连接";
        }
      }

      // 更新设备信息
      function updateDeviceInfo(deviceInfo) {
        var infoDiv = document.getElementById("deviceInfo");
        if (!infoDiv) {
          return;
        }
        infoDiv.innerHTML =
          "<p><strong>屏幕分辨率:</strong> " +
          deviceInfo.width +
          " x " +
          deviceInfo.height +
          "</p>" +
          "<p><strong>屏幕比例:</strong> " +
          deviceInfo.ratio +
          "</p>" +
          "<p><strong>更新时间:</strong> " +
          new Date().toLocaleTimeString() +
          "</p>";
      }

      // 加载配置到表单
      function loadConfigData(configData) {
        if (configData && typeof configData === "object") {
          if (configData.interval) {
            document.getElementById("interval").value = configData.interval;
          }
          if (configData.launchWait) {
            document.getElementById("launchWait").value = configData.launchWait;
          }
          if (configData.clickDuration) {
            document.getElementById("clickDuration").value =
              configData.clickDuration;
          }
          if (configData.slideDuration) {
            document.getElementById("slideDuration").value =
              configData.slideDuration;
          }
          // 配置加载成功，通过Python端日志显示
        } else {
          // 配置为空，通过Python端日志显示
        }
      }

      // 页面加载完成
      document.addEventListener("DOMContentLoaded", function () {
        // 页面加载完成，通过Python端日志显示

        // 通知Python端页面已加载完成
        if (window.airscript) {
          try {
            window.airscript.call("page_loaded", "true");
          } catch (e) {
            // 通知失败，静默处理
          }
        }
      });

      // 最小化和恢复界面切换
      function switchToMinimizedView() {
        document.body.innerHTML = `
                <div style="
                    width: 100%;
                    height: 100vh;
                    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                    display: flex;
                    flex-direction: column;
                    align-items: center;
                    justify-content: center;
                    border-radius: 15px;
                    box-shadow: 0 8px 32px rgba(0,0,0,0.3);
                    position: relative;
                    overflow: hidden;
                ">
                    <!-- 装饰性背景图案 -->
                    <div style="
                        position: absolute;
                        top: -20px;
                        right: -20px;
                        width: 40px;
                        height: 40px;
                        background: rgba(255,255,255,0.1);
                        border-radius: 50%;
                    "></div>
                    <div style="
                        position: absolute;
                        bottom: -15px;
                        left: -15px;
                        width: 30px;
                        height: 30px;
                        background: rgba(255,255,255,0.08);
                        border-radius: 50%;
                    "></div>

                    <!-- 主图标 -->
                    <div style="
                        width: 36px;
                        height: 36px;
                        background: rgba(255,255,255,0.2);
                        border-radius: 12px;
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        margin-bottom: 8px;
                        backdrop-filter: blur(10px);
                        border: 1px solid rgba(255,255,255,0.3);
                        cursor: pointer;
                    " onclick="window.airscript.call('restore_window', 'true')">
                        <div style="
                            font-size: 18px;
                            color: white;
                            font-weight: bold;
                        ">📱</div>
                    </div>

                    <!-- 状态指示器 -->
                    <div style="
                        width: 8px;
                        height: 8px;
                        background: #4CAF50;
                        border-radius: 50%;
                        margin-bottom: 6px;
                        animation: pulse 2s infinite;
                        box-shadow: 0 0 8px rgba(76, 175, 80, 0.6);
                    "></div>

                    <!-- 状态文本 -->
                    <div id="miniStatus" style="
                        font-size: 8px;
                        color: rgba(255,255,255,0.8);
                        text-align: center;
                        font-weight: 500;
                        margin-bottom: 8px;
                    ">运行中</div>

                    <!-- 隐藏的日志容器 -->
                    <div id="logContainer" style="display: none;"></div>

                    <!-- 控制按钮 -->
                    <div style="display: flex; flex-direction: column; gap: 4px;">
                        <button onclick="window.airscript.call('stop_automation', 'true')" style="
                            width: 24px;
                            height: 24px;
                            background: rgba(220, 53, 69, 0.8);
                            color: white;
                            border: none;
                            border-radius: 6px;
                            cursor: pointer;
                            font-size: 10px;
                            display: flex;
                            align-items: center;
                            justify-content: center;
                            backdrop-filter: blur(10px);
                            transition: all 0.3s ease;
                        " onmouseover="this.style.background='rgba(220, 53, 69, 1)'"
                           onmouseout="this.style.background='rgba(220, 53, 69, 0.8)'">⏹</button>
                    </div>

                    <style>
                        @keyframes pulse {
                            0% { opacity: 1; transform: scale(1); }
                            50% { opacity: 0.7; transform: scale(1.1); }
                            100% { opacity: 1; transform: scale(1); }
                        }
                    </style>
                </div>
            `;

        // 重新定义必要的全局函数
        window.updateLog = function (logEntry) {
          var container = document.getElementById("logContainer");
          if (!container) {
            return;
          }
          var logDiv = document.createElement("div");
          logDiv.className = "log-entry log-" + logEntry.level;
          logDiv.innerHTML = "[" + logEntry.time + "] " + logEntry.message;
          container.appendChild(logDiv);
          container.scrollTop = container.scrollHeight;
        };

        window.updateStatus = function (status) {
          var miniStatus = document.getElementById("miniStatus");
          if (miniStatus) {
            miniStatus.textContent = status;
          }
        };
      }

      function switchToNormalView() {
        location.reload(); // 重新加载完整界面
      }

      function restoreWindow() {
        window.airscript.call("restore_window", "true");
      }

      function minimizeWindow() {
        window.airscript.call("minimize_window", "true");
      }

      // 显示任务完成消息
      function showCompletionMessage(messageData) {
        try {
          if (typeof layui !== "undefined" && layui.layer) {
            // 使用LayUI显示成功消息
            layui.layer.alert(messageData.message, {
              icon: 1,
              title: messageData.title,
              skin: "layui-layer-molv",
              closeBtn: 1,
              anim: 2,
              btn: ["确定"],
              yes: function (index) {
                layui.layer.close(index);
              },
            });
          } else {
            // 备用方案：使用原生alert
            alert(
              messageData.title +
                "\n\n" +
                messageData.message.replace(/\\n/g, "\n")
            );
          }
        } catch (e) {
          console.error("显示完成消息失败:", e);
          // 最后的备用方案
          alert(
            "任务完成！\n\n已成功点击'回任务页'按钮\n程序执行完毕"
          );
        }
      }

      // 确保函数在全局作用域中可用
      window.updateLog = updateLog;
      window.updateStatus = updateStatus;
      window.updateHidStatus = updateHidStatus;
      window.updateDeviceInfo = updateDeviceInfo;
      window.loadConfigData = loadConfigData;
      window.switchToMinimizedView = switchToMinimizedView;
      window.switchToNormalView = switchToNormalView;
      window.showCompletionMessage = showCompletionMessage;
    </script>
  </body>
</html>
